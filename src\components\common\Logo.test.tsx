import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Logo } from './Logo';

// Mock the theme store
const mockThemeStore = {
  colors: {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    accent: '#06b6d4',
  },
};

vi.mock('../../stores/themeStore', () => ({
  useThemeStore: () => mockThemeStore,
}));

describe('Logo Component', () => {
  it('renders the full logo by default', () => {
    render(<Logo data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo')).toBeInTheDocument();
    expect(screen.getByTestId('test-logo-icon')).toBeInTheDocument();
    expect(screen.getByTestId('test-logo-text')).toBeInTheDocument();
    expect(screen.getByText('Nexed')).toBeInTheDocument();
  });

  it('renders only icon when variant is icon', () => {
    render(<Logo variant="icon" data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('test-logo-text')).not.toBeInTheDocument();
  });

  it('renders only text when variant is text', () => {
    render(<Logo variant="text" data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo-text')).toBeInTheDocument();
    expect(screen.getByText('Nexed')).toBeInTheDocument();
  });

  it('hides text when showText is false', () => {
    render(<Logo showText={false} data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo')).toBeInTheDocument();
    expect(screen.getByTestId('test-logo-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('test-logo-text')).not.toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(
      <Logo size="sm" variant="icon" data-testid="test-logo" />
    );
    expect(screen.getByTestId('test-logo-icon')).toHaveClass('h-6', 'w-6');

    rerender(<Logo size="lg" variant="icon" data-testid="test-logo" />);
    expect(screen.getByTestId('test-logo-icon')).toHaveClass('h-12', 'w-12');

    rerender(<Logo size="xl" variant="icon" data-testid="test-logo" />);
    expect(screen.getByTestId('test-logo-icon')).toHaveClass('h-16', 'w-16');
  });

  it('applies custom className', () => {
    render(<Logo className="custom-class" data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo')).toHaveClass('custom-class');
  });

  it('applies custom textClassName to text', () => {
    render(<Logo textClassName="custom-text-class" data-testid="test-logo" />);

    expect(screen.getByTestId('test-logo-text')).toHaveClass(
      'custom-text-class'
    );
  });
});
