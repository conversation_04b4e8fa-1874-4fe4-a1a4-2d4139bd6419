/**
 * ErrorFallback Storybook Stories
 * Comprehensive stories showcasing different error scenarios and configurations
 */

import type { Meta, StoryObj } from '@storybook/react-vite';
import ErrorFallback from './ErrorFallback';
import {
  createAppError,
  ErrorType,
  ErrorSeverity,
} from '../../../utils/errorTypes';

const meta: Meta<typeof ErrorFallback> = {
  title: 'Common/ErrorFallback',
  component: ErrorFallback,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Professional error fallback UI component with recovery actions and theme integration.',
      },
    },
  },
  argTypes: {
    error: {
      description: 'The error object to display',
      control: false,
    },
    resetError: {
      description: 'Function to reset the error state',
      action: 'resetError',
    },
    componentName: {
      description: 'Name of the component that errored',
      control: 'text',
    },
    level: {
      description: 'Error boundary level',
      control: 'select',
      options: ['page', 'section', 'component'],
    },
    showDetails: {
      description: 'Whether to show technical details by default',
      control: 'boolean',
    },
    enableUserFeedback: {
      description: 'Whether to enable user feedback section',
      control: 'boolean',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ErrorFallback>;

// Helper function to create mock errors
const createMockError = (
  type: ErrorType,
  severity: ErrorSeverity,
  message: string,
  userMessage?: string
) => {
  return createAppError(new Error(message), {
    type,
    severity,
    userMessage: userMessage || 'A user-friendly error message',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [],
  });
};

// Default story
export const Default: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.MEDIUM,
      'Something went wrong in the component',
      'An unexpected error occurred. Please try again.'
    ),
    componentName: 'ExampleComponent',
    level: 'component',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Network error
export const NetworkError: Story = {
  args: {
    error: createMockError(
      ErrorType.NETWORK,
      ErrorSeverity.HIGH,
      'Failed to fetch data from server',
      'Unable to connect to the server. Please check your internet connection.'
    ),
    componentName: 'DataFetcher',
    level: 'section',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Authentication error
export const AuthenticationError: Story = {
  args: {
    error: createMockError(
      ErrorType.AUTHENTICATION,
      ErrorSeverity.MEDIUM,
      'Authentication token expired',
      'Your session has expired. Please sign in again.'
    ),
    componentName: 'AuthGuard',
    level: 'page',
    showDetails: false,
    enableUserFeedback: false,
  },
};

// Critical error
export const CriticalError: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.CRITICAL,
      'Critical system failure',
      'A critical error has occurred. The application needs to be restarted.'
    ),
    componentName: 'AppCore',
    level: 'page',
    showDetails: true,
    enableUserFeedback: true,
  },
};

// Chunk load error
export const ChunkLoadError: Story = {
  args: {
    error: createMockError(
      ErrorType.CHUNK_LOAD,
      ErrorSeverity.MEDIUM,
      'Loading chunk 2 failed',
      'Failed to load application resources. Please refresh the page.'
    ),
    componentName: 'LazyComponent',
    level: 'component',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Validation error
export const ValidationError: Story = {
  args: {
    error: createMockError(
      ErrorType.VALIDATION,
      ErrorSeverity.LOW,
      'Invalid input provided',
      'Please check your input and try again.'
    ),
    componentName: 'FormField',
    level: 'component',
    showDetails: false,
    enableUserFeedback: false,
  },
};

// API error
export const APIError: Story = {
  args: {
    error: createMockError(
      ErrorType.API,
      ErrorSeverity.HIGH,
      'Internal server error (500)',
      "We're experiencing technical difficulties. Please try again later."
    ),
    componentName: 'APIClient',
    level: 'section',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Page level error
export const PageLevelError: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.HIGH,
      'Page rendering failed',
      'This page could not be loaded. Please try refreshing or go back.'
    ),
    componentName: 'HomePage',
    level: 'page',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Section level error
export const SectionLevelError: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.MEDIUM,
      'Section component crashed',
      'This section is temporarily unavailable.'
    ),
    componentName: 'DashboardSection',
    level: 'section',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Component level error
export const ComponentLevelError: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.LOW,
      'Component render error',
      'This component encountered an issue.'
    ),
    componentName: 'SmallWidget',
    level: 'component',
    showDetails: false,
    enableUserFeedback: false,
  },
};

// With technical details shown
export const WithTechnicalDetails: Story = {
  args: {
    error: createMockError(
      ErrorType.RUNTIME,
      ErrorSeverity.MEDIUM,
      'TypeError: Cannot read property "map" of undefined',
      'Unable to display the data list.'
    ),
    componentName: 'DataList',
    level: 'component',
    showDetails: true,
    enableUserFeedback: true,
  },
};

// Without user feedback
export const WithoutUserFeedback: Story = {
  args: {
    error: createMockError(
      ErrorType.VALIDATION,
      ErrorSeverity.LOW,
      'Invalid form data',
      'Please correct the highlighted fields.'
    ),
    componentName: 'ContactForm',
    level: 'component',
    showDetails: false,
    enableUserFeedback: false,
  },
};

// Unknown error type
export const UnknownError: Story = {
  args: {
    error: createMockError(
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      'An unknown error occurred',
      'Something unexpected happened. Please try again.'
    ),
    componentName: 'UnknownComponent',
    level: 'component',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Timeout error
export const TimeoutError: Story = {
  args: {
    error: createMockError(
      ErrorType.TIMEOUT,
      ErrorSeverity.MEDIUM,
      'Request timeout after 30 seconds',
      'The request is taking longer than expected. Please try again.'
    ),
    componentName: 'SlowDataLoader',
    level: 'section',
    showDetails: false,
    enableUserFeedback: true,
  },
};

// Authorization error
export const AuthorizationError: Story = {
  args: {
    error: createMockError(
      ErrorType.AUTHORIZATION,
      ErrorSeverity.MEDIUM,
      'Access denied (403)',
      "You don't have permission to access this resource."
    ),
    componentName: 'AdminPanel',
    level: 'page',
    showDetails: false,
    enableUserFeedback: true,
  },
};
