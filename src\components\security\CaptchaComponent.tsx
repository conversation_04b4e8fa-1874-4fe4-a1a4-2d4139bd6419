import { useState, useEffect, useRef } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { CheckIcon } from '../icons';

export interface CaptchaComponentProps {
  onVerify?: (token: string) => void;
  onExpire?: () => void;
  onError?: (error: string) => void;
  siteKey?: string;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
  type?: 'image' | 'audio';
  className?: string;
  'data-testid'?: string;
}

// Simple math CAPTCHA for demo purposes (in production, use reCAPTCHA)
export function CaptchaComponent({
  onVerify,
  onExpire,
  onError,
  theme = 'auto',
  size = 'normal',
  className = '',
  'data-testid': testId,
}: CaptchaComponentProps) {
  const { colors, theme: appTheme } = useThemeStore();
  const [mathProblem, setMathProblem] = useState({ question: '', answer: 0 });
  const [userAnswer, setUserAnswer] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [error, setError] = useState('');
  const [isExpired, setIsExpired] = useState(false);
  const timeoutRef = useRef<number | undefined>(undefined);

  const generateMathProblem = () => {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const operations = ['+', '-', '*'];
    const operation = operations[Math.floor(Math.random() * operations.length)];

    let answer: number;
    let question: string;

    switch (operation) {
      case '+': {
        answer = num1 + num2;
        question = `${num1} + ${num2}`;
        break;
      }
      case '-': {
        // Ensure positive result
        const larger = Math.max(num1, num2);
        const smaller = Math.min(num1, num2);
        answer = larger - smaller;
        question = `${larger} - ${smaller}`;
        break;
      }
      case '*': {
        // Use smaller numbers for multiplication
        const smallNum1 = Math.floor(Math.random() * 5) + 1;
        const smallNum2 = Math.floor(Math.random() * 5) + 1;
        answer = smallNum1 * smallNum2;
        question = `${smallNum1} × ${smallNum2}`;
        break;
      }
      default: {
        answer = num1 + num2;
        question = `${num1} + ${num2}`;
      }
    }

    setMathProblem({ question, answer });
    setUserAnswer('');
    setError('');
    setIsVerified(false);
    setIsExpired(false);

    // Set expiration timer (5 minutes)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(
      () => {
        setIsExpired(true);
        onExpire?.();
      },
      5 * 60 * 1000
    );
  };

  useEffect(() => {
    generateMathProblem();
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleVerify = () => {
    const numericAnswer = parseInt(userAnswer.trim());

    if (isNaN(numericAnswer)) {
      setError('Please enter a valid number');
      return;
    }

    if (numericAnswer === mathProblem.answer) {
      setIsVerified(true);
      setError('');
      const token = `captcha_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      onVerify?.(token);
    } else {
      setAttempts(prev => prev + 1);
      setError('Incorrect answer. Please try again.');

      if (attempts >= 2) {
        // Generate new problem after 3 failed attempts
        generateMathProblem();
        setAttempts(0);
        onError?.('Too many failed attempts. New challenge generated.');
      }
    }
  };

  const handleRefresh = () => {
    generateMathProblem();
    setAttempts(0);
  };

  const getThemeColors = () => {
    const effectiveTheme = theme === 'auto' ? appTheme : theme;
    return effectiveTheme === 'dark'
      ? { bg: colors.surface, text: colors.text, border: colors.border }
      : { bg: '#ffffff', text: '#1f2937', border: '#e5e7eb' };
  };

  const themeColors = getThemeColors();
  const sizeClasses = size === 'compact' ? 'p-3' : 'p-4';

  if (isExpired) {
    return (
      <div
        className={`${sizeClasses} border rounded-lg ${className}`}
        style={{
          backgroundColor: `${colors.error}10`,
          borderColor: colors.error,
          color: colors.error,
        }}
        data-testid={testId}
      >
        <div className="text-center">
          <p className="text-sm font-medium mb-2">CAPTCHA Expired</p>
          <button
            onClick={generateMathProblem}
            className="px-3 py-1 text-xs rounded border"
            style={{
              borderColor: colors.error,
              color: colors.error,
              backgroundColor: 'transparent',
            }}
          >
            Generate New Challenge
          </button>
        </div>
      </div>
    );
  }

  if (isVerified) {
    return (
      <div
        className={`${sizeClasses} border rounded-lg ${className}`}
        style={{
          backgroundColor: `${colors.success}10`,
          borderColor: colors.success,
          color: colors.success,
        }}
        data-testid={testId}
      >
        <div className="flex items-center justify-center">
          <CheckIcon className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">Verified</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${sizeClasses} border rounded-lg ${className}`}
      style={{
        backgroundColor: themeColors.bg,
        borderColor: themeColors.border,
        color: themeColors.text,
      }}
      data-testid={testId}
    >
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-6 h-6 mr-2 flex items-center justify-center">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <span className="text-sm font-medium">Security Check</span>
          </div>
          <button
            onClick={handleRefresh}
            className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
            aria-label="Refresh challenge"
            title="Generate new challenge"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>

        {/* Math Problem */}
        <div className="text-center">
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
            What is the result of:
          </p>
          <div
            className="text-2xl font-bold mb-3 p-2 rounded"
            style={{ backgroundColor: colors.surface }}
          >
            {mathProblem.question} = ?
          </div>
        </div>

        {/* Input */}
        <div className="flex gap-2">
          <input
            type="number"
            value={userAnswer}
            onChange={e => setUserAnswer(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleVerify()}
            placeholder="Your answer"
            className="flex-1 px-3 py-2 border rounded text-center"
            style={{
              backgroundColor: themeColors.bg,
              borderColor: error ? colors.error : themeColors.border,
              color: themeColors.text,
            }}
            data-testid="captcha-input"
          />
          <button
            onClick={handleVerify}
            disabled={!userAnswer.trim()}
            className="px-4 py-2 rounded font-medium text-sm transition-colors disabled:opacity-50"
            style={{
              backgroundColor: colors.primary,
              color: '#ffffff',
            }}
            data-testid="captcha-verify"
          >
            Verify
          </button>
        </div>

        {/* Error */}
        {error && (
          <p className="text-xs" style={{ color: colors.error }}>
            {error}
          </p>
        )}

        {/* Attempts indicator */}
        {attempts > 0 && (
          <p className="text-xs text-slate-500">Attempts: {attempts}/3</p>
        )}
      </div>
    </div>
  );
}
