import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface SettingsViewProps {
  className?: string;
  'data-testid'?: string;
}

export const SettingsView: React.FC<SettingsViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [activeTab, setActiveTab] = useState('notifications');

  const tabs = [
    { id: 'notifications', label: 'Notifications', icon: '🔔' },
    { id: 'privacy', label: 'Privacy & Security', icon: '🔒' },
    { id: 'appearance', label: 'Appearance', icon: '🎨' },
    { id: 'integrations', label: 'Integrations', icon: '🔗' },
    { id: 'advanced', label: 'Advanced', icon: '⚙️' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'notifications':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Notification Preferences
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Desktop Notifications
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Show notifications on your desktop
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Sound Notifications
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Play sound for new messages
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Email Notifications
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Receive email summaries
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Mobile Push Notifications
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Send notifications to mobile devices
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Notification Schedule
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
                    Do Not Disturb Hours
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="time"
                      defaultValue="22:00"
                      className="px-3 py-2 border rounded-lg"
                      style={{
                        borderColor: colors.border,
                        backgroundColor: colors.background,
                        color: colors.text,
                      }}
                    />
                    <span style={{ color: colors.textSecondary }}>to</span>
                    <input
                      type="time"
                      defaultValue="08:00"
                      className="px-3 py-2 border rounded-lg"
                      style={{
                        borderColor: colors.border,
                        backgroundColor: colors.background,
                        color: colors.text,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'privacy':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Privacy Settings
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Show Online Status
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Let others see when you're online
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Read Receipts
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Show when you've read messages
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Typing Indicators
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Show when you're typing
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Security
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      End-to-End Encryption
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Encrypt direct messages
                    </p>
                  </div>
                  <input type="checkbox" className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Two-Factor Authentication
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Add extra security to your account
                    </p>
                  </div>
                  <button
                    className="px-4 py-2 text-sm rounded-lg"
                    style={{
                      backgroundColor: colors.primary,
                      color: 'white',
                    }}
                  >
                    Enable
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Theme
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-full h-20 bg-white border rounded-lg mb-2"></div>
                  <p className="text-sm" style={{ color: colors.text }}>Light</p>
                </div>
                <div className="text-center">
                  <div className="w-full h-20 bg-gray-900 border rounded-lg mb-2"></div>
                  <p className="text-sm" style={{ color: colors.text }}>Dark</p>
                </div>
                <div className="text-center">
                  <div className="w-full h-20 bg-gradient-to-br from-white to-gray-900 border rounded-lg mb-2"></div>
                  <p className="text-sm" style={{ color: colors.text }}>Auto</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                Display Options
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
                    Message Density
                  </label>
                  <select
                    className="w-full px-3 py-2 border rounded-lg"
                    style={{
                      borderColor: colors.border,
                      backgroundColor: colors.background,
                      color: colors.text,
                    }}
                  >
                    <option>Comfortable</option>
                    <option>Compact</option>
                    <option>Cozy</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Show Avatars
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Display user avatars in messages
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium" style={{ color: colors.text }}>
                      Animated Emojis
                    </p>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      Enable animated emoji reactions
                    </p>
                  </div>
                  <input type="checkbox" defaultChecked className="toggle" />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <p style={{ color: colors.textSecondary }}>
              {tabs.find(tab => tab.id === activeTab)?.label} settings coming soon...
            </p>
          </div>
        );
    }
  };

  return (
    <div
      className={`flex-1 overflow-y-auto ${className}`}
      data-testid={testId}
    >
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2" style={{ color: colors.text }}>
            Settings
          </h1>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Customize your discuss experience
          </p>
        </div>

        <div className="flex gap-8">
          {/* Sidebar */}
          <div className="w-64 flex-shrink-0">
            <nav className="space-y-2">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 dark:bg-blue-900/20'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  style={{
                    color: activeTab === tab.id ? colors.primary : colors.text,
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{tab.icon}</span>
                    <span className="font-medium">{tab.label}</span>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div
              className="bg-white dark:bg-gray-800 rounded-lg p-6"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
              }}
            >
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
