import React from 'react';
import { useThemeStore } from '../../stores/themeStore';
import type { LoginMode } from './LoginScreen';

export interface AuthTab {
  id: LoginMode;
  label: string;
  icon: React.ReactNode;
  description?: string;
}

export interface AuthNavigationTabsProps {
  currentMode: LoginMode;
  onModeChange: (mode: LoginMode) => void;
  tabs?: AuthTab[];
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

const defaultTabs: AuthTab[] = [
  {
    id: 'login',
    label: 'Sign In',
    description: 'Email & Password',
    icon: (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
        />
      </svg>
    ),
  },
  {
    id: 'otp',
    label: 'OTP Login',
    description: 'Phone Verification',
    icon: (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      </svg>
    ),
  },
  {
    id: 'forgot',
    label: 'Reset',
    description: 'Forgot Password',
    icon: (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
        />
      </svg>
    ),
  },
  {
    id: 'access-request',
    label: 'Request',
    description: 'Access Request',
    icon: (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    ),
  },
];

export function AuthNavigationTabs({
  currentMode,
  onModeChange,
  tabs = defaultTabs,
  disabled = false,
  className = '',
  'data-testid': testId,
}: AuthNavigationTabsProps) {
  const { colors } = useThemeStore();

  return (
    <div
      className={`
        ${className.includes('bg-transparent') ? 'bg-transparent' : 'backdrop-blur-md bg-white/95 dark:bg-slate-900/95'}
        ${className.includes('border-0') ? '' : 'border-t border-slate-200/50 dark:border-slate-700/50'}
        ${className.includes('shadow-none') ? '' : 'shadow-lg shadow-slate-900/5 dark:shadow-slate-900/20'}
        ${className}
      `}
      data-testid={testId}
    >
      {/* Gradient overlay for better visual separation */}
      {!className.includes('border-0') && (
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-slate-300 dark:via-slate-600 to-transparent" />
      )}

      {/* Navigation content */}
      <div className="px-2 sm:px-3 py-2">
        <div className="flex items-center justify-center">
          <div className="flex gap-0.5 sm:gap-1 bg-slate-100/50 dark:bg-slate-800/50 rounded-xl p-1 max-w-full overflow-x-auto">
            {tabs.map(tab => {
              const isActive = tab.id === currentMode;

              return (
                <button
                  key={tab.id}
                  onClick={() => !disabled && onModeChange(tab.id)}
                  disabled={disabled}
                  className={`
                    relative flex flex-col items-center justify-center
                    px-2 sm:px-3 py-2 rounded-lg min-w-[60px] sm:min-w-[70px]
                    transition-all duration-300 ease-out
                    border-none group flex-shrink-0
                    ${
                      isActive
                        ? 'bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 shadow-md'
                        : 'bg-transparent text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${!isActive && !disabled ? 'hover:bg-white/60 dark:hover:bg-slate-700/60' : ''}
                    transform hover:scale-105 active:scale-95
                    touch-manipulation
                  `}
                  style={{
                    boxShadow: isActive
                      ? `0 2px 8px ${colors.primary}15`
                      : 'none',
                  }}
                  data-testid={`auth-tab-${tab.id}`}
                >
                  {/* Active background glow */}
                  {isActive && (
                    <div
                      className="absolute inset-0 rounded-lg opacity-10"
                      style={{ backgroundColor: colors.primary }}
                    />
                  )}

                  {/* Icon */}
                  <div
                    className={`
                    mb-1 transition-all duration-300 relative z-10
                    ${isActive ? 'scale-110' : 'group-hover:scale-105'}
                  `}
                  >
                    <div
                      className={`transition-colors duration-300 ${
                        isActive ? '' : 'group-hover:text-current'
                      }`}
                      style={{
                        color: isActive ? colors.primary : 'currentColor',
                      }}
                    >
                      {tab.icon}
                    </div>
                  </div>

                  {/* Label */}
                  <span
                    className={`
                    text-xs font-medium leading-tight relative z-10
                    transition-all duration-300 text-center
                    ${isActive ? 'font-semibold' : ''}
                  `}
                  >
                    {tab.label}
                  </span>

                  {/* Description - only show on active tab for compact design, hidden on mobile */}
                  {tab.description && isActive && (
                    <span className="hidden sm:block text-[10px] leading-tight mt-0.5 opacity-75 relative z-10 text-center">
                      {tab.description}
                    </span>
                  )}

                  {/* Ripple effect on click */}
                  <div
                    className={`
                    absolute inset-0 rounded-lg transition-all duration-300
                    ${isActive ? 'bg-gradient-to-br from-transparent via-white/20 to-transparent' : ''}
                  `}
                  />
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Bottom safe area for mobile devices */}
      <div
        className="pb-safe-bottom sm:pb-0"
        style={{ paddingBottom: 'env(safe-area-inset-bottom, 0px)' }}
      />
    </div>
  );
}
