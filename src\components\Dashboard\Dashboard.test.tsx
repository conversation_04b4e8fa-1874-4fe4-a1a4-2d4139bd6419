import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import Dashboard from './Dashboard';

// Mock the hooks
vi.mock('../../hooks/useKeyboardShortcuts', () => ({
  useSearchShortcuts: vi.fn(),
  useNavigationShortcuts: vi.fn(),
}));

describe('Dashboard', () => {
  const mockUser = {
    name: 'Test User',
    email: '<EMAIL>',
    isOnline: true,
  };

  it('renders dashboard with user information', () => {
    render(<Dashboard user={mockUser} />);

    // Check if the dashboard is rendered
    expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
  });

  it('renders notification bar by default', () => {
    render(<Dashboard user={mockUser} />);

    // Check if notification bar is visible
    expect(
      screen.getByText(/This database will expire in 1 month/)
    ).toBeInTheDocument();
  });

  it('can dismiss notification bar', () => {
    render(<Dashboard user={mockUser} />);

    // Find and click the dismiss button
    const dismissButton = screen.getByLabelText('Dismiss notification');
    fireEvent.click(dismissButton);

    // Check if notification bar is hidden
    expect(
      screen.queryByText(/This database will expire in 1 month/)
    ).not.toBeInTheDocument();
  });

  it('renders app tiles', () => {
    render(<Dashboard user={mockUser} />);

    // Check if app tiles are rendered
    expect(screen.getByTestId('app-tile-1')).toBeInTheDocument();
    expect(screen.getByText('Sales')).toBeInTheDocument();
    expect(screen.getByText('Inventory')).toBeInTheDocument();
  });

  it('renders search overlay when opened', () => {
    render(<Dashboard user={mockUser} />);

    // The search overlay should not be visible initially
    expect(screen.queryByTestId('search-overlay')).not.toBeInTheDocument();
  });

  it('renders top navigation with user info', () => {
    render(<Dashboard user={mockUser} />);

    // Check if user avatar/initial is rendered
    expect(
      screen.getByLabelText(`User menu for ${mockUser.name}`)
    ).toBeInTheDocument();
  });
});
