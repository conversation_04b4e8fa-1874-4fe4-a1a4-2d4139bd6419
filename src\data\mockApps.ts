// DEPRECATED: This file is deprecated. Use the new MSW infrastructure instead.
// Import from: import { useAppsDataStore } from '../stores/appsDataStore';
// Or use API endpoints directly: fetch('/api/apps')

// Legacy interface for backward compatibility
export interface AppData {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  gradient?: boolean;
  isActive?: boolean;
  isPremium?: boolean;
  navLinks?: Array<{
    label: string;
    href: string;
    isActive?: boolean;
  }>;
  views?: Record<
    string,
    {
      title: string;
      content: string;
    }
  >;
}

export const mockApps: AppData[] = [
  {
    id: '1',
    title: 'Sales',
    description: 'Manage sales orders and customers',
    icon: '💼',
    color: '#3b82f6', // blue-500
    gradient: true,
    isActive: true,
    navLinks: [
      {
        label: 'Dashboard',
        href: '/app?menu=1&view=dashboard',
        isActive: true,
      },
      { label: 'Orders', href: '/app?menu=1&view=orders', isActive: false },
      {
        label: 'Customers',
        href: '/app?menu=1&view=customers',
        isActive: false,
      },
      { label: 'Products', href: '/app?menu=1&view=products', isActive: false },
      { label: 'Reports', href: '/app?menu=1&view=reports', isActive: false },
    ],
    views: {
      dashboard: {
        title: 'Sales Dashboard',
        content:
          'Welcome to the Sales Dashboard. Here you can view your sales metrics and performance.',
      },
      orders: {
        title: 'Sales Orders',
        content: 'Manage your sales orders and track their status.',
      },
      customers: {
        title: 'Customer Management',
        content: 'View and manage your customer database.',
      },
      products: {
        title: 'Product Catalog',
        content: 'Manage your product catalog and pricing.',
      },
      reports: {
        title: 'Sales Reports',
        content: 'Generate and view sales reports and analytics.',
      },
    },
  },
  {
    id: '2',
    title: 'Inventory',
    description: 'Track products and stock levels',
    icon: '📦',
    color: '#10b981', // emerald-500
    gradient: true,
    isActive: true,
  },
  {
    id: '3',
    title: 'Accounting',
    description: 'Financial management and reporting',
    icon: '💰',
    color: '#f59e0b', // amber-500
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '4',
    title: 'HR',
    description: 'Human resources and payroll',
    icon: '👥',
    color: '#8b5cf6', // violet-500
    gradient: true,
    isActive: true,
  },
  {
    id: '5',
    title: 'CRM',
    description: 'Customer relationship management',
    icon: '🤝',
    color: '#06b6d4', // cyan-500
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '6',
    title: 'Project',
    description: 'Project management and tracking',
    icon: '📊',
    color: '#84cc16', // lime-500
    gradient: true,
    isActive: true,
  },
  {
    id: '7',
    title: 'Manufacturing',
    description: 'Production planning and control',
    icon: '🏭',
    color: '#6366f1', // indigo-500
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '8',
    title: 'Purchase',
    description: 'Vendor management and procurement',
    icon: '🛒',
    color: '#ec4899', // pink-500
    gradient: true,
    isActive: true,
  },
  {
    id: '9',
    title: 'Website',
    description: 'E-commerce and web presence',
    icon: '🌐',
    color: '#14b8a6', // teal-500
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '10',
    title: 'Discuss',
    description: 'Team communication and chat',
    icon: '💬',
    color: '#f97316', // orange-500 - matching the attached image
    gradient: true,
    isActive: true,
    navLinks: [
      { label: 'Channels', href: '/app?menu=10&view=channels', isActive: true },
      {
        label: 'Direct Messages',
        href: '/app?menu=10&view=messages',
        isActive: false,
      },
      { label: 'Teams', href: '/app?menu=10&view=teams', isActive: false },
      {
        label: 'Settings',
        href: '/app?menu=10&view=settings',
        isActive: false,
      },
      {
        label: 'Admin',
        href: '/app?menu=10&view=admin',
        isActive: false,
      },
    ],
    views: {
      channels: {
        title: 'Team Channels',
        content: 'Communicate with your team through organized channels.',
      },
      messages: {
        title: 'Direct Messages',
        content: 'Private conversations with team members.',
      },
      teams: {
        title: 'Team Management',
        content: 'Manage your teams and team members.',
      },
      settings: {
        title: 'Discussion Settings',
        content: 'Configure your discussion preferences and notifications.',
      },
      admin: {
        title: 'Administration',
        content: 'Manage bots, automation, archival, and integrations.',
      },
    },
  },
  {
    id: '11',
    title: 'Calendar',
    description: 'Schedule and appointments',
    icon: '📅',
    color: '#ef4444', // red-500
    gradient: true,
    isActive: true,
  },
  {
    id: '12',
    title: 'Documents',
    description: 'File management and sharing',
    icon: '📁',
    color: '#64748b', // slate-500
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  // Premium modules
  {
    id: '13',
    title: 'AI Analytics',
    description: 'Advanced AI-powered business analytics',
    icon: '🤖',
    color: '#7c3aed', // violet-600
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '14',
    title: 'Advanced Reports',
    description: 'Enterprise-grade reporting and insights',
    icon: '📊',
    color: '#dc2626', // red-600
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '15',
    title: 'Workflow Automation',
    description: 'Automate business processes and workflows',
    icon: '⚡',
    color: '#059669', // emerald-600
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '16',
    title: 'Multi-Company',
    description: 'Manage multiple companies in one platform',
    icon: '🏢',
    color: '#0891b2', // cyan-600
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '17',
    title: 'API Integration',
    description: 'Connect with third-party services and APIs',
    icon: '🔗',
    color: '#ea580c', // orange-600
    gradient: true,
    isActive: false,
    isPremium: true,
  },
  {
    id: '18',
    title: 'Advanced Security',
    description: 'Enterprise security and compliance tools',
    icon: '🛡️',
    color: '#be123c', // rose-700
    gradient: true,
    isActive: false,
    isPremium: true,
  },
];

// DEPRECATED: Use the new MSW infrastructure instead
// These functions are kept for backward compatibility but will be removed in a future version

// Helper function to get app data by ID
export const getAppById = (id: string): AppData | undefined => {
  console.warn(
    'getAppById is deprecated. Use useAppsDataStore().getAppById() or fetch("/api/apps/:id") instead.'
  );
  return mockApps.find(app => app.id === id);
};

// Helper function to get apps data as a map for quick lookup
export const getAppsMap = (): Record<string, AppData> => {
  console.warn(
    'getAppsMap is deprecated. Use useAppsDataStore().apps or fetch("/api/apps") instead.'
  );
  return mockApps.reduce(
    (acc, app) => {
      acc[app.id] = app;
      return acc;
    },
    {} as Record<string, AppData>
  );
};
