import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import DynamicAppBottomBar from '../DynamicAppBottomBar';
import { GridIcon, ListIcon } from '../../../icons';

// Mock module
vi.mock('../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      surface: '#ffffff',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      mutedForeground: '#9ca3af',
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      hover: '#f3f4f6',
      background: '#f9fafb',
      error: '#ef4444',
      errorForeground: '#ffffff',
    },
  }),
}));

const mockHandlers = {
  onNew: () => console.log('New clicked'),
  onExport: () => console.log('Export clicked'),
  onSearch: (query: string) => console.log('Search:', query),
  onNext: () => console.log('Next page'),
  onPrev: () => console.log('Previous page'),
};

const mockView = {
  title: 'Test Dashboard',
  breadcrumbs: [
    { label: 'Home', onClick: () => console.log('Home clicked') },
    { label: 'Projects', onClick: () => console.log('Projects clicked') },
  ],
  actions: [
    { label: 'New', onClick: mockHandlers.onNew, isPrimary: true, variant: 'filled' as const },
    { label: 'Export', onClick: mockHandlers.onExport, variant: 'outline' as const },
  ],
  search: {
    onSearch: mockHandlers.onSearch,
  },
  pagination: {
    currentRange: '1-20 of 100',
    onNext: mockHandlers.onNext,
    onPrev: mockHandlers.onPrev,
  },
  viewModes: [
    { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
    { name: 'list', icon: <ListIcon className="w-4 h-4" /> },
  ],
  activeViewMode: 'grid',
};

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('DynamicAppBottomBar', () => {
  it('renders the component with basic props', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
    expect(screen.getByText('New')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
    expect(screen.getByText('1-20 of 100')).toBeInTheDocument();
  });

  it('renders view mode switcher buttons', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    const gridButton = screen.getByLabelText('Switch to grid view');
    const listButton = screen.getByLabelText('Switch to list view');
    
    expect(gridButton).toBeInTheDocument();
    expect(listButton).toBeInTheDocument();
  });

  it('shows mobile search toggle', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    const searchToggle = screen.getByLabelText('Toggle search');
    expect(searchToggle).toBeInTheDocument();
  });

  it('expands mobile search when toggle is clicked', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    const searchToggle = screen.getByLabelText('Toggle search');
    fireEvent.click(searchToggle);
    
    // Check if search input appears (there are multiple, so use getAllBy)
    const searchInputs = screen.getAllByPlaceholderText('Search...');
    expect(searchInputs.length).toBeGreaterThan(0);
  });

  it('renders with custom className', () => {
    const { container } = renderWithRouter(
      <DynamicAppBottomBar view={mockView} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('renders with test id', () => {
    renderWithRouter(
      <DynamicAppBottomBar view={mockView} data-testid="bottom-bar" />
    );
    
    expect(screen.getByTestId('bottom-bar')).toBeInTheDocument();
  });

  it('renders pagination controls', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    const prevButton = screen.getByLabelText('Previous page');
    const nextButton = screen.getByLabelText('Next page');
    
    expect(prevButton).toBeInTheDocument();
    expect(nextButton).toBeInTheDocument();
  });

  it('renders floating action button for primary actions', () => {
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    // The floating action button should be present for primary actions
    const floatingButtons = screen.getAllByLabelText('New');
    expect(floatingButtons.length).toBeGreaterThan(0);
  });

  it('renders filter tags when provided in mobile search', () => {
    const viewWithFilters = {
      ...mockView,
      search: {
        ...mockView.search,
        filters: [
          { id: '1', label: 'Active' },
          { id: '2', label: 'High Priority' },
        ],
      },
    };

    renderWithRouter(<DynamicAppBottomBar view={viewWithFilters} />);
    
    // Open mobile search to see filters
    const searchToggle = screen.getByLabelText('Toggle search');
    fireEvent.click(searchToggle);
    
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('High Priority')).toBeInTheDocument();
  });

  it('handles search form submission', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    renderWithRouter(<DynamicAppBottomBar view={mockView} />);
    
    // Open mobile search
    const searchToggle = screen.getByLabelText('Toggle search');
    fireEvent.click(searchToggle);
    
    // Type in search input (get all and use the mobile one)
    const searchInputs = screen.getAllByPlaceholderText('Search...');
    const mobileSearchInput = searchInputs.find(input =>
      input.closest('.sm\\:hidden') || input.closest('[class*="sm:hidden"]')
    ) || searchInputs[1]; // fallback to second input if mobile class not found
    
    if (mobileSearchInput) {
      fireEvent.change(mobileSearchInput, { target: { value: 'test query' } });
      
      // Submit form
      const form = mobileSearchInput.closest('form');
      if (form) {
        fireEvent.submit(form);
      }
    }
    
    consoleSpy.mockRestore();
  });

  describe('Breadcrumb functionality', () => {
    it('renders breadcrumb with title as last item', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Projects')).toBeInTheDocument();
      expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
    });

    it('renders breadcrumb without breadcrumbs prop (title only)', () => {
      const viewWithoutBreadcrumbs = {
        ...mockView,
        breadcrumbs: undefined,
      };
      
      renderWithRouter(<DynamicAppBottomBar view={viewWithoutBreadcrumbs} />);
      
      expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
      expect(screen.queryByText('Home')).not.toBeInTheDocument();
      expect(screen.queryByText('Projects')).not.toBeInTheDocument();
    });

    it('handles breadcrumb item clicks', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      const homeButton = screen.getByText('Home');
      fireEvent.click(homeButton);
      
      expect(consoleSpy).toHaveBeenCalledWith('Home clicked');
      
      consoleSpy.mockRestore();
    });

    it('renders separator between actions and breadcrumb when actions exist', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      // Check that both actions and breadcrumb are present
      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.getByText('Home')).toBeInTheDocument();
    });
  });

  describe('Search dropdown functionality', () => {
    it('renders chevron dropdown button in desktop search', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      const chevronButtons = screen.getAllByLabelText('Toggle search options');
      expect(chevronButtons.length).toBeGreaterThan(0);
    });

    it('renders chevron dropdown button in mobile search', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      // Open mobile search
      const searchToggle = screen.getByLabelText('Toggle search');
      fireEvent.click(searchToggle);
      
      const chevronButtons = screen.getAllByLabelText('Toggle search options');
      expect(chevronButtons.length).toBeGreaterThan(0);
    });

    it('handles chevron dropdown click', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      const chevronButton = screen.getAllByLabelText('Toggle search options')[0];
      fireEvent.click(chevronButton);
      
      // The click should not throw an error
      expect(chevronButton).toBeInTheDocument();
    });

    it('handles action button variants correctly', () => {
      const viewWithVariants = {
        ...mockView,
        actions: [
          { label: 'Primary', onClick: () => {}, isPrimary: true, variant: 'filled' as const },
          { label: 'Outline', onClick: () => {}, variant: 'outline' as const },
          { label: 'Default', onClick: () => {} },
        ],
      };

      renderWithRouter(<DynamicAppBottomBar view={viewWithVariants} />);
      
      expect(screen.getByText('Primary')).toBeInTheDocument();
      expect(screen.getByText('Outline')).toBeInTheDocument();
      expect(screen.getByText('Default')).toBeInTheDocument();
    });

    it('shows confirmation dialog when action has confirm prop', () => {
      const viewWithConfirm = {
        ...mockView,
        actions: [
          {
            label: 'Delete',
            onClick: () => console.log('Delete confirmed'),
            confirm: {
              title: 'Confirm Delete',
              message: 'Are you sure you want to delete this item?',
              confirmText: 'Delete',
              cancelText: 'Cancel'
            }
          },
        ],
      };

      renderWithRouter(<DynamicAppBottomBar view={viewWithConfirm} />);
      
      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);
      
      // Check if confirmation dialog appears
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete this item?')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('executes action after confirmation', () => {
      const mockDeleteHandler = vi.fn();
      const viewWithConfirm = {
        ...mockView,
        actions: [
          {
            label: 'Delete',
            onClick: mockDeleteHandler,
            confirm: {
              title: 'Confirm Delete',
              message: 'Are you sure?'
            }
          },
        ],
      };

      renderWithRouter(<DynamicAppBottomBar view={viewWithConfirm} />);
      
      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);
      
      // Confirm the action
      const confirmButton = screen.getByText('Confirm');
      fireEvent.click(confirmButton);
      
      expect(mockDeleteHandler).toHaveBeenCalled();
    });

    it('cancels action when cancel is clicked', () => {
      const mockDeleteHandler = vi.fn();
      const viewWithConfirm = {
        ...mockView,
        actions: [
          {
            label: 'Delete',
            onClick: mockDeleteHandler,
            confirm: {
              title: 'Confirm Delete',
              message: 'Are you sure?'
            }
          },
        ],
      };

      renderWithRouter(<DynamicAppBottomBar view={viewWithConfirm} />);
      
      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);
      
      // Cancel the action
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      expect(mockDeleteHandler).not.toHaveBeenCalled();
      expect(screen.queryByText('Confirm Delete')).not.toBeInTheDocument();
    });
  });
});