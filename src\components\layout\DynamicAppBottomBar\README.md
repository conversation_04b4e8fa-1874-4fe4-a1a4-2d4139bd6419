# DynamicAppBottomBar Component

A standalone bottom bar component for contextual controls, separated from `DynamicAppHeader` for better extendability and reduced complexity.

## Overview

The `DynamicAppBottomBar` component provides contextual controls for application views, including:
- View title and action buttons
- Advanced search functionality with filters and favorites
- Pagination controls
- View mode switcher
- Mobile-responsive design with floating action buttons

## Features

### 🎯 **Separation of Concerns**
- **Focused Responsibility**: Handles only bottom bar functionality
- **Independent**: Can be used without the main header
- **Extensible**: Easy to modify and extend without affecting other components

### 🔍 **Advanced Search**
- Filter tags with color coding
- Dropdown filter items
- Group by options
- Favorite searches
- Custom filter and group creation
- Mobile-optimized search overlay

### 📱 **Mobile Responsive**
- Collapsible search on mobile devices
- Floating action buttons for primary actions
- Touch-friendly controls

### 🎨 **Theme Integration**
- Full theme store integration
- Dynamic color adaptation
- Consistent hover states

## Usage

### Basic Usage

```tsx
import { DynamicAppBottomBar } from '../../../components/layout';

const MyComponent = () => {
  const view = {
    title: 'My Dashboard',
    actions: [
      { label: 'New', onClick: () => console.log('New'), isPrimary: true },
      { label: 'Export', onClick: () => console.log('Export') },
    ],
    search: {
      onSearch: (query: string) => console.log('Search:', query),
    },
    pagination: {
      currentRange: '1-20 of 100',
      onNext: () => console.log('Next'),
      onPrev: () => console.log('Previous'),
    },
    viewModes: [
      { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
      { name: 'list', icon: <ListIcon className="w-4 h-4" /> },
    ],
    activeViewMode: 'grid',
  };

  return <DynamicAppBottomBar view={view} />;
};
```

### With DynamicAppHeader

```tsx
import { DynamicAppHeader } from '../../../components/layout';

const AppWithHeader = () => {
  const headerProps = {
    app: { /* app config */ },
    user: { /* user config */ },
    view: { /* view config - same as bottom bar */ },
  };

  return <DynamicAppHeader {...headerProps} />;
  // The header automatically includes the bottom bar
};
```

### Advanced Search Configuration

```tsx
const advancedView = {
  title: 'Advanced Dashboard',
  actions: [/* actions */],
  search: {
    // Filter tags (active filters)
    filterTags: [
      { id: '1', label: 'Active', color: 'blue' },
      { id: '2', label: 'High Priority', color: 'red' },
    ],
    
    // Available filter options
    filterItems: [
      { 
        id: 'status', 
        label: 'Status', 
        options: ['Active', 'Completed', 'On Hold'] 
      },
      { 
        id: 'priority', 
        label: 'Priority', 
        options: ['High', 'Medium', 'Low'] 
      },
    ],
    
    // Group by options
    groupByItems: [
      { id: 'status', label: 'Status' },
      { id: 'assignee', label: 'Assignee' },
    ],
    
    // Saved favorite searches
    favoriteItems: [
      { id: '1', label: 'My Active Projects' },
      { id: '2', label: 'High Priority Tasks' },
    ],
    
    // Event handlers
    onSearch: (query: string) => { /* handle search */ },
    onTagRemove: (tagId: string) => { /* remove filter tag */ },
    onFilterSelect: (filterId: string) => { /* apply filter */ },
    onGroupBySelect: (groupId: string) => { /* group results */ },
    onFavoriteSelect: (favoriteId: string) => { /* load favorite */ },
    onFavoriteDelete: (favoriteId: string) => { /* delete favorite */ },
    onAddCustomFilter: () => { /* show custom filter dialog */ },
    onAddCustomGroup: () => { /* show custom group dialog */ },
    onSaveCurrentSearch: () => { /* save current search as favorite */ },
  },
  // ... rest of config
};
```

## Props

### DynamicAppBottomBarProps

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `view` | `ViewConfig` | ✅ | View configuration object |
| `className` | `string` | ❌ | Additional CSS classes |
| `data-testid` | `string` | ❌ | Test identifier |

### ViewConfig

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `title` | `string` | ✅ | View title displayed on the left |
| `actions` | `ActionConfig[]` | ✅ | Action buttons |
| `search` | `SearchConfig` | ✅ | Search configuration |
| `pagination` | `PaginationConfig` | ✅ | Pagination controls |
| `viewModes` | `ViewModeConfig[]` | ✅ | Available view modes |
| `activeViewMode` | `string` | ✅ | Currently active view mode |

## Migration from DynamicAppHeader

If you were using the old combined component, here's how to migrate:

### Before (Combined Component)
```tsx
// Old way - everything in one component
<DynamicAppHeader 
  app={appConfig}
  user={userConfig}
  view={viewConfig}
/>
```

### After (Separated Components)
```tsx
// New way - header automatically includes bottom bar
<DynamicAppHeader 
  app={appConfig}
  user={userConfig}
  view={viewConfig}
/>

// Or use bottom bar independently
<DynamicAppBottomBar view={viewConfig} />
```

## Benefits of Separation

### 1. **Better Extendability**
- Modify bottom bar without affecting header
- Add new features to specific components
- Easier to test individual components

### 2. **Reduced Complexity**
- Smaller, focused components
- Clearer separation of concerns
- Easier to understand and maintain

### 3. **Reusability**
- Use bottom bar in different contexts
- Compose layouts more flexibly
- Better component architecture

### 4. **Performance**
- Smaller bundle sizes when using individually
- Better tree shaking
- Optimized re-renders

## Styling

The component uses the theme store for consistent styling:

```tsx
// Custom styling
<DynamicAppBottomBar 
  view={viewConfig}
  className="custom-bottom-bar"
/>
```

## Accessibility

- Full keyboard navigation support
- ARIA labels for all interactive elements
- Screen reader friendly
- Focus management

## Examples

See the Storybook stories for comprehensive examples:
- `DynamicAppBottomBar.stories.tsx` - Standalone component examples
- `DynamicAppHeader.stories.tsx` - Integrated header examples