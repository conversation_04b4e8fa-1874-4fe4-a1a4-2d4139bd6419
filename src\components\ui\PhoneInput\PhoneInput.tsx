import React from 'react';
import { PhoneInput as ReactPhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { useThemeStore } from '../../../stores/themeStore';
import { Label } from '../Label';
import { Caption } from '../Caption';
import { cn } from '../../../utils/cn';

export interface PhoneInputProps {
  value: string;
  onChange: (phone: string) => void;
  label?: string;
  placeholder?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  defaultCountry?: string;
  fullWidth?: boolean;
  className?: string;
  'data-testid'?: string;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  label,
  placeholder = 'Enter phone number',
  error,
  helperText,
  required = false,
  disabled = false,
  defaultCountry = 'us',
  fullWidth = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const containerClasses = cn(fullWidth ? 'w-full' : '', className);

  const phoneInputStyles = {
    '--react-international-phone-height': '40px',
    '--react-international-phone-background-color': colors.input,
    '--react-international-phone-text-color': colors.inputForeground,
    '--react-international-phone-border-color': error
      ? colors.error
      : colors.border,
    '--react-international-phone-border-radius': '8px',
    '--react-international-phone-font-size': '14px',
    '--react-international-phone-disabled-background-color': colors.disabled,
    '--react-international-phone-disabled-text-color':
      colors.disabledForeground,
    '--react-international-phone-dropdown-shadow': `0 4px 6px -1px ${colors.shadow}20, 0 2px 4px -1px ${colors.shadow}10`,
    '--react-international-phone-dropdown-background-color': colors.surface,
    '--react-international-phone-dropdown-item-background-color':
      colors.surface,
    '--react-international-phone-dropdown-item-text-color': colors.text,
    '--react-international-phone-selected-dropdown-item-background-color':
      colors.hover,
    '--react-international-phone-focused-dropdown-item-background-color':
      colors.hover,
  } as React.CSSProperties;

  return (
    <div className={containerClasses} data-testid={testId}>
      {label && (
        <Label
          required={required}
          className="mb-2"
          color={error ? 'error' : 'inherit'}
          disabled={disabled}
        >
          {label}
        </Label>
      )}

      <div className="relative">
        <ReactPhoneInput
          defaultCountry={defaultCountry}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          style={phoneInputStyles}
          className={cn(
            'w-full transition-all duration-200',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          inputProps={{}}
        />
      </div>

      {(error || helperText) && (
        <div className="mt-1">
          {error && (
            <Caption variant="error" size="xs">
              {error}
            </Caption>
          )}
          {helperText && !error && (
            <Caption variant="helper" size="xs">
              {helperText}
            </Caption>
          )}
        </div>
      )}
    </div>
  );
};

export default PhoneInput;
