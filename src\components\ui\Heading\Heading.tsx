import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  size?:
    | 'xs'
    | 'sm'
    | 'base'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  color?:
    | 'primary'
    | 'secondary'
    | 'muted'
    | 'error'
    | 'warning'
    | 'success'
    | 'info'
    | 'inherit';
  align?: 'left' | 'center' | 'right';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  truncate?: boolean;
  lineClamp?: 1 | 2 | 3;
  'data-testid'?: string;
}

const Heading: React.FC<HeadingProps> = ({
  children,
  level = 2,
  size,
  weight,
  color = 'inherit',
  align = 'left',
  transform = 'none',
  truncate = false,
  lineClamp,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  // Level-based defaults
  const levelDefaults = {
    1: { size: '4xl' as const, weight: 'bold' as const },
    2: { size: '3xl' as const, weight: 'bold' as const },
    3: { size: '2xl' as const, weight: 'semibold' as const },
    4: { size: 'xl' as const, weight: 'semibold' as const },
    5: { size: 'lg' as const, weight: 'medium' as const },
    6: { size: 'base' as const, weight: 'medium' as const },
  };

  const finalSize = size || levelDefaults[level].size;
  const finalWeight = weight || levelDefaults[level].weight;

  // Size classes
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
    '5xl': 'text-5xl',
    '6xl': 'text-6xl',
  };

  // Weight classes
  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
    extrabold: 'font-extrabold',
  };

  // Alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  // Transform classes
  const transformClasses = {
    none: '',
    uppercase: 'uppercase',
    lowercase: 'lowercase',
    capitalize: 'capitalize',
  };

  // Color mapping with theme-aware colors
  const getTextColor = () => {
    switch (color) {
      case 'primary':
        return colors.primary;
      case 'secondary':
        return colors.textSecondary;
      case 'muted':
        return colors.mutedForeground;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      case 'success':
        return colors.success;
      case 'info':
        return colors.info;
      case 'inherit':
      default:
        return colors.text;
    }
  };

  // Build classes
  const classes = cn(
    sizeClasses[finalSize],
    weightClasses[finalWeight],
    alignClasses[align],
    transformClasses[transform],
    truncate && 'truncate',
    lineClamp && `line-clamp-${lineClamp}`,
    'leading-tight', // Better line height for headings
    className
  );

  const style = {
    color: getTextColor(),
    ...props.style,
  };

  // Dynamic component based on level
  const Component = `h${level}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

  return React.createElement(
    Component,
    {
      className: classes,
      style: style,
      'data-testid': testId,
      ...props,
    },
    children
  );
};

export default Heading;
