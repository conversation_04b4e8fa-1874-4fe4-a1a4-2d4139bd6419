import { Dashboard } from '../components';
import { ErrorBoundary } from '../components/common';

export default function DashboardPage() {
  // In a real app, user data would come from authentication context/store
  const user = {
    name: 'Admin User',
    email: '<EMAIL>',
    isOnline: true,
  };

  return (
    <ErrorBoundary
      level="page"
      componentName="DashboardPage"
      enableAutoRecovery={true}
      enableReporting={true}
    >
      <Dashboard user={user} data-testid="dashboard-page" />
    </ErrorBoundary>
  );
}
