import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface Company {
  id: string;
  name: string;
  logo?: string;
  domain?: string;
  isActive: boolean;
  settings?: {
    currency: string;
    timezone: string;
    language: string;
    dateFormat: string;
  };
  createdAt?: string;
  updatedAt?: string;
  description?: string;
  industry?: string;
  size?: 'small' | 'medium' | 'large' | 'enterprise';
  location?: {
    country: string;
    city: string;
    address?: string;
  };
  contact?: {
    email: string;
    phone: string;
    website?: string;
  };
}

interface CompanyState {
  companies: Company[];
  currentCompany: Company | null;
  isLoading: boolean;
  error?: string;

  // API Actions
  fetchCompanies: () => Promise<void>;
  fetchCompany: (id: string) => Promise<Company | null>;
  createCompany: (company: Omit<Company, 'id'>) => Promise<Company | null>;
  updateCompany: (
    id: string,
    updates: Partial<Company>
  ) => Promise<Company | null>;
  deleteCompany: (id: string) => Promise<boolean>;

  // Local Actions
  setCompanies: (companies: Company[]) => void;
  setCurrentCompany: (company: Company) => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  clearError: () => void;

  // Utility functions
  getCompanyById: (id: string) => Company | undefined;
  getActiveCompanies: () => Company[];
  switchCompany: (companyId: string) => void;
}

// Helper function to get auth token
const getAuthToken = (): string | null => {
  // In a real app, you'd get this from your auth store or localStorage
  return localStorage.getItem('authToken');
};

// Helper function to make authenticated API calls
const apiCall = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const token = getAuthToken();
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  return fetch(url, {
    ...options,
    headers,
  });
};

export const useCompanyStore = create<CompanyState>()(
  devtools(
    persist(
      (set, get) => ({
        companies: [],
        currentCompany: null,
        isLoading: false,
        error: undefined,

        // API Actions
        fetchCompanies: async () => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall('/api/companies');
            if (!response.ok) {
              throw new Error('Failed to fetch companies');
            }
            const result = await response.json();
            const companies = result.data || result; // Handle paginated response
            set({ companies, isLoading: false });

            // Set current company if none is selected
            if (!get().currentCompany && companies.length > 0) {
              set({ currentCompany: companies[0] });
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
          }
        },

        fetchCompany: async (id: string) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/companies/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch company');
            }
            const company = await response.json();

            // Update the company in the list
            set(state => ({
              companies: state.companies.map(c => (c.id === id ? company : c)),
              isLoading: false,
            }));

            return company;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        createCompany: async (companyData: Omit<Company, 'id'>) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall('/api/companies', {
              method: 'POST',
              body: JSON.stringify(companyData),
            });
            if (!response.ok) {
              throw new Error('Failed to create company');
            }
            const newCompany = await response.json();

            set(state => ({
              companies: [...state.companies, newCompany],
              isLoading: false,
            }));

            return newCompany;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        updateCompany: async (id: string, updates: Partial<Company>) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/companies/${id}`, {
              method: 'PUT',
              body: JSON.stringify(updates),
            });
            if (!response.ok) {
              throw new Error('Failed to update company');
            }
            const updatedCompany = await response.json();

            set(state => ({
              companies: state.companies.map(company =>
                company.id === id ? updatedCompany : company
              ),
              currentCompany:
                state.currentCompany?.id === id
                  ? updatedCompany
                  : state.currentCompany,
              isLoading: false,
            }));

            return updatedCompany;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return null;
          }
        },

        deleteCompany: async (id: string) => {
          set({ isLoading: true, error: undefined });
          try {
            const response = await apiCall(`/api/companies/${id}`, {
              method: 'DELETE',
            });
            if (!response.ok) {
              throw new Error('Failed to delete company');
            }

            set(state => {
              const filteredCompanies = state.companies.filter(
                company => company.id !== id
              );
              const newCurrentCompany =
                state.currentCompany?.id === id
                  ? filteredCompanies[0] || null
                  : state.currentCompany;

              return {
                companies: filteredCompanies,
                currentCompany: newCurrentCompany,
                isLoading: false,
              };
            });

            return true;
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              isLoading: false,
            });
            return false;
          }
        },

        // Local Actions
        setCompanies: (companies: Company[]) => {
          set({ companies });
        },

        setCurrentCompany: (company: Company) => {
          set({ currentCompany: company });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error?: string) => {
          set({ error });
        },

        clearError: () => {
          set({ error: undefined });
        },

        getCompanyById: (id: string) => {
          return get().companies.find(company => company.id === id);
        },

        getActiveCompanies: () => {
          return get().companies.filter(company => company.isActive);
        },

        switchCompany: (companyId: string) => {
          const company = get().getCompanyById(companyId);
          if (company) {
            get().setCurrentCompany(company);
          }
        },
      }),
      {
        name: 'company-store',
        partialize: state => ({
          currentCompany: state.currentCompany,
          companies: state.companies,
        }),
      }
    ),
    {
      name: 'company-store',
    }
  )
);

// Helper function to get current company
export const getCurrentCompany = () => {
  return useCompanyStore.getState().currentCompany;
};

// Helper function to switch company
export const switchToCompany = (companyId: string) => {
  useCompanyStore.getState().switchCompany(companyId);
};
