import type { Meta, StoryObj } from '@storybook/react-vite';
import Separator from './Separator';

const meta: Meta<typeof Separator> = {
  title: 'UI/Separator',
  component: Separator,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A versatile separator component with multiple visual styles for dividing content sections. Features theme-aware styling and various decorative options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['line', 'dots', 'wave', 'gradient', 'ornamental', 'geometric'],
      description: 'Visual style of the separator',
    },
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
      description: 'Orientation of the separator',
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
      description: 'Size of the separator',
    },
    spacing: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Spacing around the separator',
    },
    opacity: {
      control: { type: 'range', min: 0.1, max: 1, step: 0.1 },
      description: 'Opacity of the separator',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Variants: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <p className="text-sm text-gray-600 mb-2">Line</p>
        <Separator variant="line" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Dots</p>
        <Separator variant="dots" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Wave</p>
        <Separator variant="wave" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Gradient</p>
        <Separator variant="gradient" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Ornamental</p>
        <Separator variant="ornamental" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Geometric</p>
        <Separator variant="geometric" />
      </div>
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="w-80 space-y-6">
      <div>
        <p className="text-sm text-gray-600 mb-2">Small</p>
        <Separator size="sm" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Medium</p>
        <Separator size="md" />
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">Large</p>
        <Separator size="lg" />
      </div>
    </div>
  ),
};

export const Spacing: Story = {
  render: () => (
    <div className="w-80">
      <p className="text-sm text-gray-600">Small spacing</p>
      <Separator spacing="sm" />
      <p className="text-sm text-gray-600">Medium spacing</p>
      <Separator spacing="md" />
      <p className="text-sm text-gray-600">Large spacing</p>
      <Separator spacing="lg" />
      <p className="text-sm text-gray-600">Extra large spacing</p>
      <Separator spacing="xl" />
      <p className="text-sm text-gray-600">End</p>
    </div>
  ),
};

export const Vertical: Story = {
  render: () => (
    <div className="flex h-32 items-center space-x-4">
      <span>Left</span>
      <Separator orientation="vertical" variant="line" />
      <span>Center</span>
      <Separator orientation="vertical" variant="dots" />
      <span>Right</span>
    </div>
  ),
};

export const Creative: Story = {
  render: () => (
    <div className="w-80 space-y-8">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Welcome</h3>
        <Separator variant="ornamental" spacing="lg" />
        <p className="text-sm text-gray-600 mt-4">Please sign in to continue</p>
      </div>
    </div>
  ),
};
