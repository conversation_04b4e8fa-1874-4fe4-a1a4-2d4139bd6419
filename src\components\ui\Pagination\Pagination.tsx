import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { ChevronLeftIcon, ChevronRightIcon } from '../../icons';

export interface PaginationProps {
  currentRange: string;
  onNext: () => void;
  onPrev: () => void;
  className?: string;
  'data-testid'?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentRange,
  onNext,
  onPrev,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div 
      className={cn('flex items-center space-x-1', className)}
      data-testid={testId}
    >
      <button
        onClick={onPrev}
        className="p-2 rounded-md transition-colors duration-150"
        style={{
          backgroundColor: 'transparent',
          color: colors.mutedForeground,
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = `${colors.hover}15`;
          e.currentTarget.style.color = colors.text;
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = colors.mutedForeground;
        }}
        aria-label="Previous page"
      >
        <ChevronLeftIcon className="w-4 h-4" />
      </button>
      
      <span
        className="text-sm font-medium px-3 py-1"
        style={{ color: colors.textSecondary }}
      >
        {currentRange}
      </span>
      
      <button
        onClick={onNext}
        className="p-2 rounded-md transition-colors duration-150"
        style={{
          backgroundColor: 'transparent',
          color: colors.mutedForeground,
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = `${colors.hover}15`;
          e.currentTarget.style.color = colors.text;
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = colors.mutedForeground;
        }}
        aria-label="Next page"
      >
        <ChevronRightIcon className="w-4 h-4" />
      </button>
    </div>
  );
};

export default Pagination;
