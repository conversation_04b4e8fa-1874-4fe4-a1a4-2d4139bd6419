import React, { useState, useRef, useCallback } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface FileUploadProps {
  onFileSelect: (files: FileList) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export interface FilePreview {
  id: string;
  file: File;
  url?: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  progress?: number;
  error?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  accept = "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar",
  multiple = true,
  maxSize = 10 * 1024 * 1024, // 10MB default
  maxFiles = 5,
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isDragOver, setIsDragOver] = useState(false);
  const [previews, setPreviews] = useState<FilePreview[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileType = (file: File): FilePreview['type'] => {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    if (file.type.startsWith('audio/')) return 'audio';
    if (file.type.includes('pdf') || file.type.includes('document') || file.type.includes('text')) {
      return 'document';
    }
    return 'other';
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size exceeds ${(maxSize / 1024 / 1024).toFixed(1)}MB limit`;
    }
    return null;
  };

  const createFilePreview = useCallback((file: File): FilePreview => {
    const type = getFileType(file);
    const preview: FilePreview = {
      id: `${file.name}-${Date.now()}`,
      file,
      type,
      error: validateFile(file),
    };

    // Create preview URL for images and videos
    if (type === 'image' || type === 'video') {
      preview.url = URL.createObjectURL(file);
    }

    return preview;
  }, [maxSize]);

  const handleFileSelect = useCallback((files: FileList) => {
    const fileArray = Array.from(files);
    
    if (fileArray.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const newPreviews = fileArray.map(createFilePreview);
    setPreviews(newPreviews);
    
    // Only pass valid files to parent
    const validFiles = newPreviews.filter(p => !p.error).map(p => p.file);
    if (validFiles.length > 0) {
      const dataTransfer = new DataTransfer();
      validFiles.forEach(file => dataTransfer.items.add(file));
      onFileSelect(dataTransfer.files);
    }
  }, [maxFiles, createFilePreview, onFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const removePreview = useCallback((id: string) => {
    setPreviews(prev => {
      const updated = prev.filter(p => p.id !== id);
      // Revoke object URLs to prevent memory leaks
      const removed = prev.find(p => p.id === id);
      if (removed?.url) {
        URL.revokeObjectURL(removed.url);
      }
      return updated;
    });
  }, []);

  const getFileIcon = (type: FilePreview['type']) => {
    switch (type) {
      case 'image': return '🖼️';
      case 'video': return '🎥';
      case 'audio': return '🎵';
      case 'document': return '📄';
      default: return '📎';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={className} data-testid={testId}>
      {/* Drop Zone */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
          isDragOver ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' : ''
        }`}
        style={{
          borderColor: isDragOver ? colors.primary : colors.border,
          backgroundColor: isDragOver ? `${colors.primary}10` : colors.backgroundSecondary,
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <div className="space-y-2">
          <div className="text-4xl">📁</div>
          <div>
            <p className="text-sm font-medium" style={{ color: colors.text }}>
              Drop files here or click to browse
            </p>
            <p className="text-xs" style={{ color: colors.textSecondary }}>
              Max {maxFiles} files, {(maxSize / 1024 / 1024).toFixed(1)}MB each
            </p>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          disabled={disabled}
          className="hidden"
          onChange={handleInputChange}
        />
      </div>

      {/* File Previews */}
      {previews.length > 0 && (
        <div className="mt-4 space-y-2">
          {previews.map((preview) => (
            <div
              key={preview.id}
              className={`flex items-center space-x-3 p-3 border rounded-lg ${
                preview.error ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : ''
              }`}
              style={{
                borderColor: preview.error ? colors.error : colors.border,
                backgroundColor: preview.error ? `${colors.error}10` : colors.backgroundSecondary,
              }}
            >
              {/* Preview */}
              <div className="flex-shrink-0">
                {preview.url && preview.type === 'image' ? (
                  <img
                    src={preview.url}
                    alt={preview.file.name}
                    className="w-12 h-12 object-cover rounded"
                  />
                ) : (
                  <div className="w-12 h-12 flex items-center justify-center text-2xl">
                    {getFileIcon(preview.type)}
                  </div>
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                  {preview.file.name}
                </p>
                <p className="text-xs" style={{ color: colors.textSecondary }}>
                  {formatFileSize(preview.file.size)}
                </p>
                {preview.error && (
                  <p className="text-xs" style={{ color: colors.error }}>
                    {preview.error}
                  </p>
                )}
              </div>

              {/* Progress or Remove */}
              <div className="flex-shrink-0">
                {preview.progress !== undefined ? (
                  <div className="w-8 h-8 flex items-center justify-center">
                    <div className="text-xs" style={{ color: colors.text }}>
                      {preview.progress}%
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removePreview(preview.id);
                    }}
                    className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    style={{ color: colors.textSecondary }}
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
