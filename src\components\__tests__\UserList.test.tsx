import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { UserList } from '../UserList';
import { useUsers } from '../../hooks/useSWR';
import { useThemeStore } from '../../stores/themeStore';

// Mock the hooks
vi.mock('../../hooks/useSWR');
vi.mock('../../stores/themeStore');

const mockUseUsers = vi.mocked(useUsers);
const mockUseThemeStore = vi.mocked(useThemeStore);

describe('UserList', () => {
  beforeEach(() => {
    mockUseThemeStore.mockReturnValue({
      colors: {
        primary: '#3b82f6',
        secondary: '#6366f1',
      },
    });
  });

  it('renders loading state', () => {
    mockUseUsers.mockReturnValue({
      data: [],
      error: null,
      isLoading: true,
      isError: false,
      mutate: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Loading users...')).toBeInTheDocument();
  });

  it('renders error state', () => {
    mockUseUsers.mockReturnValue({
      data: [],
      error: { message: 'Failed to load users' },
      isLoading: false,
      isError: true,
      mutate: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Failed to load users')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('renders users list', () => {
    const mockUsers = [
      { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'admin' },
      { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
    ];

    mockUseUsers.mockReturnValue({
      data: mockUsers,
      error: null,
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Users (2)')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.getByText('user')).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseUsers.mockReturnValue({
      data: [],
      error: null,
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Users (0)')).toBeInTheDocument();
    expect(screen.getByText('No users found')).toBeInTheDocument();
  });
});
