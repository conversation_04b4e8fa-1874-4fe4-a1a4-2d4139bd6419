import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { VerificationMethodButtons } from './VerificationMethodButtons';

const meta: Meta<typeof VerificationMethodButtons> = {
  title: 'Auth/VerificationMethodButtons',
  component: VerificationMethodButtons,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Elegant verification method buttons for WhatsApp, SMS, and voice call verification.',
      },
    },
  },
  argTypes: {
    layout: {
      control: 'select',
      options: ['vertical', 'horizontal', 'grid'],
      description: 'Layout arrangement of the buttons',
    },
    showDescriptions: {
      control: 'boolean',
      description: 'Show description text for each method',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable all buttons',
    },
    loading: {
      control: 'boolean',
      description: 'Show loading state',
    },
  },
};

export default meta;
type Story = StoryObj<typeof VerificationMethodButtons>;

const InteractiveWrapper = ({ initialMethod = 'whatsapp', ...args }: any) => {
  const [selectedMethod, setSelectedMethod] = useState<
    'whatsapp' | 'sms' | 'call'
  >(initialMethod);

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white dark:bg-slate-900 rounded-lg">
      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
        Choose Verification Method
      </h3>
      <VerificationMethodButtons
        selectedMethod={selectedMethod}
        onMethodSelect={setSelectedMethod}
        {...args}
      />
      <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Selected:{' '}
          <strong className="text-slate-900 dark:text-slate-100">
            {selectedMethod}
          </strong>
        </p>
      </div>
    </div>
  );
};

export const Default: Story = {
  render: args => <InteractiveWrapper {...args} />,
};

export const WithPhoneNumber: Story = {
  args: {
    phoneNumber: '+****************',
  },
  render: args => <InteractiveWrapper {...args} />,
};

export const HorizontalLayout: Story = {
  args: {
    layout: 'horizontal',
    showDescriptions: false,
  },
  render: args => (
    <div className="w-full max-w-2xl mx-auto p-6 bg-white dark:bg-slate-900 rounded-lg">
      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
        Horizontal Layout
      </h3>
      <InteractiveWrapper {...args} />
    </div>
  ),
};

export const GridLayout: Story = {
  args: {
    layout: 'grid',
  },
  render: args => (
    <div className="w-full max-w-2xl mx-auto p-6 bg-white dark:bg-slate-900 rounded-lg">
      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
        Grid Layout
      </h3>
      <InteractiveWrapper {...args} />
    </div>
  ),
};

export const WithoutDescriptions: Story = {
  args: {
    showDescriptions: false,
  },
  render: args => <InteractiveWrapper {...args} />,
};

export const WhatsAppSelected: Story = {
  render: args => <InteractiveWrapper initialMethod="whatsapp" {...args} />,
};

export const SMSSelected: Story = {
  render: args => <InteractiveWrapper initialMethod="sms" {...args} />,
};

export const VoiceCallSelected: Story = {
  render: args => <InteractiveWrapper initialMethod="call" {...args} />,
};

export const LoadingState: Story = {
  args: {
    loading: true,
  },
  render: args => <InteractiveWrapper {...args} />,
};

export const DisabledState: Story = {
  args: {
    disabled: true,
  },
  render: args => <InteractiveWrapper {...args} />,
};

export const CompactHorizontal: Story = {
  args: {
    layout: 'horizontal',
    showDescriptions: false,
    phoneNumber: '+****************',
  },
  render: args => (
    <div className="w-full max-w-lg mx-auto p-4 bg-white dark:bg-slate-900 rounded-lg">
      <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-3">
        Quick Verification
      </h3>
      <InteractiveWrapper {...args} />
    </div>
  ),
};
