import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { useSearchParams } from 'react-router-dom';

export interface SidebarProps {
  className?: string;
  'data-testid'?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const currentView = searchParams.get('view') || 'channels';

  const sidebarItems = [
    {
      id: 'channels',
      label: 'Channels',
      icon: '📢',
      count: 5,
      items: [
        { id: 'general', name: 'general', unread: 3, mentions: 1 },
        { id: 'development', name: 'development', unread: 0, mentions: 0 },
        { id: 'design', name: 'design', unread: 2, mentions: 0 },
        { id: 'marketing', name: 'marketing', unread: 1, mentions: 0 },
        { id: 'random', name: 'random', unread: 0, mentions: 0 },
      ],
    },
    {
      id: 'messages',
      label: 'Direct Messages',
      icon: '💬',
      count: 3,
      items: [
        { id: 'john-doe', name: 'John Doe', unread: 2, mentions: 1, status: 'online' },
        { id: 'jane-smith', name: 'Jane Smith', unread: 0, mentions: 0, status: 'away' },
        { id: 'mike-wilson', name: '<PERSON> <PERSON>', unread: 1, mentions: 0, status: 'offline' },
      ],
    },
    {
      id: 'teams',
      label: 'Teams',
      icon: '👥',
      count: 2,
      items: [
        { id: 'frontend-team', name: 'Frontend Team', unread: 1, mentions: 0 },
        { id: 'backend-team', name: 'Backend Team', unread: 0, mentions: 0 },
      ],
    },
  ];

  const handleViewChange = (view: string) => {
    setSearchParams(prev => {
      prev.set('view', view);
      return prev;
    });
  };

  const handleItemClick = (sectionId: string, itemId: string) => {
    console.log(`Clicked ${sectionId}:${itemId}`);
    // TODO: Navigate to specific channel/conversation
  };

  return (
    <aside
      className={`${
        isCollapsed ? 'w-16' : 'w-80'
      } border-r transition-all duration-300 ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderRightColor: colors.border,
      }}
      data-testid={testId}
    >
      {/* Sidebar Header */}
      <div className="p-4 border-b" style={{ borderBottomColor: colors.border }}>
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <h2 className="text-lg font-semibold" style={{ color: colors.text }}>
              Discuss
            </h2>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
            style={{ color: colors.textSecondary }}
          >
            {isCollapsed ? '→' : '←'}
          </button>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto">
        {sidebarItems.map(section => (
          <div key={section.id} className="mb-4">
            {/* Section Header */}
            <div
              className={`px-4 py-2 flex items-center justify-between cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                currentView === section.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
              onClick={() => handleViewChange(section.id)}
            >
              <div className="flex items-center space-x-2">
                <span className="text-lg">{section.icon}</span>
                {!isCollapsed && (
                  <>
                    <span
                      className="font-medium"
                      style={{
                        color: currentView === section.id ? colors.primary : colors.text,
                      }}
                    >
                      {section.label}
                    </span>
                    {section.count > 0 && (
                      <span
                        className="text-xs px-2 py-1 rounded-full"
                        style={{
                          backgroundColor: colors.textSecondary,
                          color: colors.surface,
                        }}
                      >
                        {section.count}
                      </span>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Section Items */}
            {!isCollapsed && currentView === section.id && (
              <div className="pl-4">
                {section.items.map(item => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between px-4 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg mx-2"
                    onClick={() => handleItemClick(section.id, item.id)}
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      {section.id === 'messages' && (
                        <div
                          className={`w-2 h-2 rounded-full ${
                            (item as any).status === 'online'
                              ? 'bg-green-500'
                              : (item as any).status === 'away'
                              ? 'bg-yellow-500'
                              : 'bg-gray-400'
                          }`}
                        />
                      )}
                      <span
                        className="text-sm truncate"
                        style={{ color: colors.text }}
                      >
                        {section.id === 'channels' ? `#${item.name}` : item.name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {item.mentions > 0 && (
                        <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                          {item.mentions}
                        </span>
                      )}
                      {item.unread > 0 && item.mentions === 0 && (
                        <span
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: colors.primary }}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Sidebar Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t" style={{ borderTopColor: colors.border }}>
          <button
            className="w-full px-3 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            style={{ color: colors.textSecondary }}
            onClick={() => handleViewChange('settings')}
          >
            ⚙️ Settings
          </button>
        </div>
      )}
    </aside>
  );
};
