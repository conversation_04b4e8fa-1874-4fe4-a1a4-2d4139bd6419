# Enhanced Error Boundary System

A comprehensive, modern React error boundary system with advanced error handling, recovery, reporting, and accessibility features.

## Features

### 🚀 Core Features
- **Enhanced Error Catching**: Catches both synchronous and asynchronous errors
- **Automatic Recovery**: Intelligent auto-recovery with exponential backoff
- **Performance Monitoring**: Built-in performance metrics and monitoring
- **Accessibility Support**: Full ARIA support and screen reader announcements
- **Error Reporting**: Comprehensive error logging and remote reporting
- **Graceful Degradation**: Fallback UI for repeated errors

### 🎯 Advanced Features
- **Custom Recovery Strategies**: Define custom recovery logic for specific error types
- **Error Throttling**: Prevents spam from identical errors
- **Global Error Handling**: Application-wide error capture and management
- **Error History Tracking**: Maintains error history for debugging
- **Progressive Recovery**: Visual progress indicators during recovery
- **Context-Aware Errors**: Rich error context with user interaction data

## Components

### ErrorBoundary
The main error boundary component with enhanced features.

```tsx
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

<ErrorBoundary
  level="component"
  componentName="MyComponent"
  enableAutoRecovery={true}
  enableReporting={true}
  enablePerformanceMonitoring={true}
  enableAccessibilityFeatures={true}
  customRecoveryStrategies={[
    {
      name: 'Custom Retry',
      condition: (error) => error.type === 'NETWORK',
      handler: async (error) => {
        // Custom recovery logic
        return true; // Return true if recovery successful
      },
    },
  ]}
>
  <MyComponent />
</ErrorBoundary>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Components to wrap with error boundary |
| `level` | `'page' \| 'section' \| 'component'` | `'component'` | Error boundary level for appropriate UI |
| `componentName` | `string` | - | Name of the component for debugging |
| `enableAutoRecovery` | `boolean` | `true` | Enable automatic error recovery |
| `enableReporting` | `boolean` | `true` | Enable error reporting |
| `enablePerformanceMonitoring` | `boolean` | `false` | Enable performance monitoring |
| `enableAccessibilityFeatures` | `boolean` | `false` | Enable accessibility features |
| `gracefulDegradation` | `boolean` | `false` | Enable graceful degradation for repeated errors |
| `maxRetryAttempts` | `number` | `3` | Maximum retry attempts |
| `customRecoveryStrategies` | `RecoveryStrategy[]` | - | Custom recovery strategies |

### ErrorBoundaryProvider
Global error boundary provider for application-wide error handling.

```tsx
import { ErrorBoundaryProvider } from '@/components/common/ErrorBoundary';

<ErrorBoundaryProvider
  enableGlobalErrorHandling={true}
  enableUnhandledRejectionCapture={true}
  maxGlobalErrors={10}
  onGlobalError={(error) => {
    // Handle global errors
  }}
>
  <App />
</ErrorBoundaryProvider>
```

### ErrorFallback
Enhanced fallback UI component with recovery actions and user feedback.

```tsx
import { ErrorFallback } from '@/components/common/ErrorBoundary';

<ErrorFallback
  error={error}
  resetError={resetError}
  componentName="MyComponent"
  level="component"
  performanceMetrics={metrics}
  retryCount={retryCount}
  onFeedback={(feedback) => {
    // Handle user feedback
  }}
/>
```

## Hooks

### useErrorBoundary
Hook for programmatic error boundary interaction.

```tsx
import { useErrorBoundary } from '@/components/common/ErrorBoundary';

const MyComponent = () => {
  const { captureError, showBoundary } = useErrorBoundary({
    componentName: 'MyComponent',
    enableAutoReport: true,
  });

  const handleAsyncError = async () => {
    try {
      await riskyAsyncOperation();
    } catch (error) {
      captureError(error, { operation: 'riskyAsyncOperation' });
    }
  };

  const handleCriticalError = () => {
    // This will trigger the nearest error boundary
    showBoundary(new Error('Critical error occurred'));
  };

  return (
    <div>
      <button onClick={handleAsyncError}>Risky Operation</button>
      <button onClick={handleCriticalError}>Critical Error</button>
    </div>
  );
};
```

### useErrorBoundaryContext
Hook to access global error boundary context.

```tsx
import { useErrorBoundaryContext } from '@/components/common/ErrorBoundary';

const ErrorStats = () => {
  const { 
    errorHistory, 
    globalErrorCount, 
    reportError, 
    clearErrors 
  } = useErrorBoundaryContext();

  return (
    <div>
      <p>Total errors: {globalErrorCount}</p>
      <p>Recent errors: {errorHistory.length}</p>
      <button onClick={clearErrors}>Clear Errors</button>
    </div>
  );
};
```

### useAsyncError
Hook for handling async errors in functional components.

```tsx
import { useAsyncError } from '@/components/common/ErrorBoundary';

const AsyncComponent = () => {
  const throwAsyncError = useAsyncError();

  useEffect(() => {
    fetchData().catch(throwAsyncError);
  }, [throwAsyncError]);

  return <div>Async Component</div>;
};
```

### useErrorHandler
Higher-order hook for wrapping async operations with error handling.

```tsx
import { useErrorHandler } from '@/components/common/ErrorBoundary';

const DataComponent = () => {
  const fetchDataWithErrorHandling = useErrorHandler(
    async (id: string) => {
      const response = await fetch(`/api/data/${id}`);
      return response.json();
    },
    { componentName: 'DataComponent' }
  );

  const handleFetch = () => {
    fetchDataWithErrorHandling('123');
  };

  return <button onClick={handleFetch}>Fetch Data</button>;
};
```

## Higher-Order Component

### withErrorBoundary
HOC for wrapping components with error boundaries.

```tsx
import { withErrorBoundary } from '@/components/common/ErrorBoundary';

const MyComponent = ({ data }) => {
  return <div>{data.value}</div>;
};

const WrappedComponent = withErrorBoundary(MyComponent, {
  enableAutoRecovery: true,
  enableReporting: true,
  level: 'component',
});

export default WrappedComponent;
```

## Error Types and Classification

The system includes comprehensive error classification:

```tsx
import { ErrorType, ErrorSeverity, createAppError } from '@/components/common/ErrorBoundary';

// Create a classified error
const error = createAppError(
  new Error('Network request failed'),
  'NETWORK_TIMEOUT',
  {
    component: 'DataFetcher',
    route: '/dashboard',
    additionalData: { requestId: '123' }
  }
);
```

### Error Types
- `NETWORK` - Network-related errors
- `API` - API response errors
- `TIMEOUT` - Request timeout errors
- `RUNTIME` - JavaScript runtime errors
- `BOUNDARY` - Error boundary errors
- `CHUNK_LOAD` - Code splitting/chunk loading errors
- `VALIDATION` - Input validation errors
- `AUTHENTICATION` - Authentication errors
- `AUTHORIZATION` - Authorization errors
- `CONFIGURATION` - Configuration errors
- `STORAGE` - Storage-related errors
- `UNKNOWN` - Unclassified errors

### Error Severity Levels
- `LOW` - Minor issues, app continues normally
- `MEDIUM` - Noticeable issues, some features affected
- `HIGH` - Major issues, significant functionality lost
- `CRITICAL` - App-breaking issues, requires immediate attention

## Recovery Strategies

The system supports various recovery strategies:

- **Retry** - Retry the failed operation
- **Reload** - Refresh the current page
- **Navigate Back** - Return to the previous page
- **Reset Component** - Reset the current component
- **Clear Cache** - Clear application cache and reload
- **Logout** - Sign out and return to login
- **Contact Support** - Get help from support team

## Configuration

### Error Reporting Configuration

```tsx
import { configureErrorReporting } from '@/components/common/ErrorBoundary';

configureErrorReporting({
  enableConsoleLogging: true,
  enableRemoteReporting: true,
  reportingEndpoint: 'https://api.example.com/errors',
  apiKey: 'your-api-key',
  environment: 'production',
  maxErrorsPerSession: 50,
});
```

## Best Practices

### 1. Error Boundary Placement
- Place page-level boundaries at route components
- Use section-level boundaries for major UI sections
- Apply component-level boundaries to complex components

### 2. Error Classification
- Always provide meaningful error messages
- Include relevant context information
- Use appropriate severity levels

### 3. Recovery Strategies
- Implement custom recovery strategies for domain-specific errors
- Provide clear user guidance in error messages
- Test recovery scenarios thoroughly

### 4. Performance Considerations
- Enable performance monitoring in development
- Monitor error frequency and patterns
- Use error throttling to prevent spam

### 5. Accessibility
- Enable accessibility features for public-facing applications
- Provide clear error announcements
- Ensure keyboard navigation works in error states

## Examples

### Basic Usage
```tsx
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

const App = () => (
  <ErrorBoundary
    level="page"
    componentName="App"
    enableAutoRecovery={true}
    enableReporting={true}
  >
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/dashboard" element={<Dashboard />} />
      </Routes>
    </Router>
  </ErrorBoundary>
);
```

### Advanced Usage with Provider
```tsx
import { 
  ErrorBoundaryProvider, 
  ErrorBoundary,
  useErrorBoundaryContext 
} from '@/components/common/ErrorBoundary';

const App = () => (
  <ErrorBoundaryProvider
    enableGlobalErrorHandling={true}
    enableUnhandledRejectionCapture={true}
    onGlobalError={(error) => {
      // Send to analytics
      analytics.track('error_occurred', {
        type: error.type,
        severity: error.severity,
        component: error.context?.component,
      });
    }}
  >
    <ErrorBoundary level="page" componentName="App">
      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route 
            path="/dashboard" 
            element={
              <ErrorBoundary 
                level="section" 
                componentName="Dashboard"
                customRecoveryStrategies={[
                  {
                    name: 'Refresh Data',
                    condition: (error) => error.type === 'API',
                    handler: async () => {
                      await refreshDashboardData();
                      return true;
                    },
                  },
                ]}
              >
                <Dashboard />
              </ErrorBoundary>
            } 
          />
        </Routes>
      </Router>
    </ErrorBoundary>
  </ErrorBoundaryProvider>
);
```

## Testing

The error boundary system includes comprehensive test utilities:

```tsx
import { render, screen } from '@testing-library/react';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

const ThrowError = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

test('catches and displays errors', () => {
  render(
    <ErrorBoundary componentName="TestComponent">
      <ThrowError shouldThrow={true} />
    </ErrorBoundary>
  );

  expect(screen.getByText('Component Error')).toBeInTheDocument();
});
```

## Migration Guide

### From Basic Error Boundary

```tsx
// Before
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.log(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}

// After
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

<ErrorBoundary
  componentName="MyComponent"
  enableAutoRecovery={true}
  enableReporting={true}
>
  {children}
</ErrorBoundary>
```

## Contributing

When contributing to the error boundary system:

1. Add comprehensive tests for new features
2. Update documentation for API changes
3. Follow accessibility guidelines
4. Consider performance implications
5. Test error scenarios thoroughly

## License

This error boundary system is part of the application's component library and follows the same license terms.