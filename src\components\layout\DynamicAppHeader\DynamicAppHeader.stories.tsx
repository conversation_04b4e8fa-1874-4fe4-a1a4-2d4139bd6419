import { <PERSON><PERSON><PERSON>Router } from 'react-router-dom';
import DynamicAppHeader from './DynamicAppHeader';
import { BellIcon, GridIcon, ListIcon, BarChartIcon, SearchIcon } from '../../icons';

// Mock Storybook types for demonstration
type Meta<T> = {
  title: string;
  component: React.ComponentType<any>;
  parameters?: any;
  decorators?: any[];
  argTypes?: any;
};

type StoryObj<T> = {
  args: T;
};

const meta: Meta<any> = {
  title: 'Layout/DynamicAppHeader',
  component: DynamicAppHeader,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A focused application header component for top navigation. Use with AppDynamicContent for complete layout functionality.',
      },
    },
  },
  decorators: [
    (Story: any) => (
      <BrowserRouter>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <Story />
          <div className="p-8">
            <div className="max-w-4xl mx-auto space-y-6">
              {Array.from({ length: 10 }, (_, i) => (
                <div
                  key={i}
                  className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Content Block {i + 1}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    This is sample content to demonstrate the header and bottom bar behavior.
                    The bottom bar is now a separate component that can be easily extended
                    and customized independently from the main header.
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </BrowserRouter>
    ),
  ],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof DynamicAppHeader>;

// Mock data
const mockApp = {
  name: 'Project Manager',
  icon: <GridIcon className="w-6 h-6" />,
  navLinks: [
    { label: 'Dashboard', href: '/dashboard', isActive: true },
    { label: 'Projects', href: '/projects' },
    { label: 'Tasks', href: '/tasks' },
    { label: 'Reports', href: '/reports' },
  ],
};

const mockUser = {
  name: 'John Doe',
  avatar: (
    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
      JD
    </div>
  ),
  notifications: [
    {
      count: 3,
      icon: <BellIcon className="w-5 h-5" />,
    },
  ],
};



export const Default: Story = {
  args: {
    app: mockApp,
    user: mockUser,
  },
};

export const WithManyNotifications: Story = {
  args: {
    app: mockApp,
    user: {
      ...mockUser,
      notifications: [
        {
          count: 99,
          icon: <BellIcon className="w-5 h-5" />,
        },
      ],
    },
  },
};

export const WithLongAppName: Story = {
  args: {
    app: {
      ...mockApp,
      name: 'Enterprise Resource Planning System',
    },
    user: mockUser,
  },
};

export const WithMultipleNavLinks: Story = {
  args: {
    app: {
      ...mockApp,
      navLinks: [
        { label: 'Dashboard', href: '/dashboard', isActive: true },
        { label: 'Projects', href: '/projects' },
        { label: 'Tasks', href: '/tasks' },
        { label: 'Reports', href: '/reports' },
        { label: 'Analytics', href: '/analytics' },
        { label: 'Settings', href: '/settings' },
      ],
    },
    user: mockUser,
  },
};

export const MinimalView: Story = {
  args: {
    app: {
      name: 'Simple App',
      icon: <GridIcon className="w-6 h-6" />,
      navLinks: [
        { label: 'Home', href: '/', isActive: true },
      ],
    },
    user: {
      name: 'User',
      avatar: (
        <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
          U
        </div>
      ),
      notifications: [],
    },
    view: {
      title: 'Dashboard',
      actions: [
        { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true },
      ],
      search: {
        onSearch: (query: string) => console.log('Search:', query),
      },
      pagination: {
        currentRange: '1-10 of 10',
        onNext: () => console.log('Next page'),
        onPrev: () => console.log('Previous page'),
      },
      viewModes: [
        { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
      ],
      activeViewMode: 'grid',
    },
  },
};
