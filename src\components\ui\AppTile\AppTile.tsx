import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface AppTileProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
  color?: string; // Primary color for the app
  gradient?: boolean; // Whether to use gradient background for icon
  isActive?: boolean; // Whether the app is active/available
  isPremium?: boolean; // Whether this is a premium app
}

const AppTile: React.FC<AppTileProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
  className = '',
  'data-testid': testId,
  color = '#f97316', // Default orange color
  gradient = true,
  isActive = true,
  isPremium = false,
}) => {
  const { colors, isDark } = useThemeStore();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const isInactive = !isActive;
  
  const baseClasses = cn(
    'relative group cursor-pointer',
    'transition-all duration-300 ease-out',
    'focus:outline-none',
    'flex flex-col items-center justify-center text-center',
    'px-2 py-4 min-h-[100px]',
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    isInactive && 'opacity-60',
    className
  );

  // No background - transparent tile
  const tileStyles = {
    backgroundColor: 'transparent',
  };

  const hoverStyles = {
    transform: 'translateY(-2px) scale(1.01)',
    boxShadow: isDark
      ? `0 12px 30px -8px ${color}30, 0 8px 20px -5px rgba(0, 0, 0, 0.4)`
      : `0 12px 30px -8px ${color}20, 0 8px 20px -5px rgba(0, 0, 0, 0.1)`,
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.95)'
      : 'rgba(248, 250, 252, 1)',
    borderRadius: '12px',
  };

  // Create gradient background for icon
  const getIconBackground = () => {
    if (!gradient) return color;

    // Create gradient based on the primary color
    const baseColor = color;
    const lighterColor = adjustColorBrightness(baseColor, 20);
    const darkerColor = adjustColorBrightness(baseColor, -20);

    return `linear-gradient(135deg, ${lighterColor} 0%, ${baseColor} 50%, ${darkerColor} 100%)`;
  };

  // Helper function to adjust color brightness
  const adjustColorBrightness = (hex: string, percent: number) => {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;
    return (
      '#' +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  };

  return (
    <div
      className={baseClasses}
      style={tileStyles}
      onClick={handleClick}
      data-testid={testId}
      onMouseEnter={e => {
        if (!disabled) {
          setIsHovered(true);
          Object.assign(e.currentTarget.style, hoverStyles);
        }
      }}
      onMouseLeave={e => {
        if (!disabled) {
          setIsHovered(false);
          Object.assign(e.currentTarget.style, tileStyles);
        }
      }}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-disabled={disabled}
    >

      {/* Smooth Description Overlay */}
      {description && (
        <div
          className={cn(
            'absolute inset-0 rounded-xl flex items-center justify-center p-4',
            'transition-all duration-500 ease-out z-30',
            isHovered
              ? 'opacity-100 backdrop-blur-md'
              : 'opacity-0 pointer-events-none'
          )}
          style={{
            background: isDark
              ? 'rgba(15, 23, 42, 0.95)'
              : 'rgba(255, 255, 255, 0.95)',
            borderRadius: '12px',
          }}
        >
          <div className="text-center max-w-full">
            <div 
              className="text-sm font-medium mb-2"
              style={{ color: colors.text }}
            >
              {title}
            </div>
            <div
              className="text-xs leading-relaxed opacity-80"
              style={{ color: colors.textSecondary }}
            >
              {description}
            </div>
          </div>
        </div>
      )}

      {/* Premium Badge */}
      {isPremium && (
        <div className="absolute top-2 right-2 z-20">
          <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg animate-pulse">
            🚫 Inactive
          </div>
        </div>
      )}

      <div className="relative z-10 flex flex-col items-center text-center space-y-3 sm:space-y-4">
        {/* Icon - plain without background container */}
        {icon && (
          <div className="relative">
            <div
              className={cn(
                "flex items-center justify-center transition-all duration-300",
                "text-4xl sm:text-5xl",
                !isInactive && "group-hover:scale-105",
                isInactive && "grayscale opacity-50"
              )}
              style={{
                color: color,
              }}
            >
              {icon}
            </div>
            {/* Small highlight dot - only show for active apps */}
            {isActive && (
              <div
                className="absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-80"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4))',
                }}
              />
            )}
          </div>
        )}

        <div className="space-y-1">
          <h3
            className={cn(
              "font-semibold leading-tight text-sm sm:text-base",
              isInactive && "opacity-70"
            )}
            style={{ color: colors.text }}
          >
            {title}
          </h3>
        </div>
      </div>
    </div>
  );
};

export default AppTile;
