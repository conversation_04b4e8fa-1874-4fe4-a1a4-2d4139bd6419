import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { SocialLoginButtons, SocialLoginDivider } from './SocialLoginButtons';

const meta: Meta<typeof SocialLoginButtons> = {
  title: 'Auth/SocialLoginButtons',
  component: SocialLoginButtons,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Social login buttons for Google, Facebook, LinkedIn, and Microsoft with customizable layouts and accessibility features.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    layout: {
      control: { type: 'select' },
      options: ['vertical', 'horizontal', 'grid'],
      description: 'Layout arrangement of the buttons',
    },
    showLabels: {
      control: { type: 'boolean' },
      description: 'Whether to show provider names on buttons',
    },
    loading: {
      control: { type: 'boolean' },
      description: 'Loading state for all buttons',
    },
    disabled: {
      control: { type: 'boolean' },
      description: 'Disabled state for all buttons',
    },
    onGoogleLogin: {
      action: 'google-login',
      description: 'Callback for Google login',
    },
    onFacebookLogin: {
      action: 'facebook-login',
      description: 'Callback for Facebook login',
    },
    onLinkedInLogin: {
      action: 'linkedin-login',
      description: 'Callback for LinkedIn login',
    },
    onMicrosoftLogin: {
      action: 'microsoft-login',
      description: 'Callback for Microsoft login',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    layout: 'vertical',
    showLabels: true,
    loading: false,
    disabled: false,
  },
};

export const Horizontal: Story = {
  args: {
    layout: 'horizontal',
    showLabels: true,
    loading: false,
    disabled: false,
  },
};

export const Grid: Story = {
  args: {
    layout: 'grid',
    showLabels: true,
    loading: false,
    disabled: false,
  },
};

export const IconsOnly: Story = {
  args: {
    layout: 'horizontal',
    showLabels: false,
    loading: false,
    disabled: false,
  },
};

export const GridIconsOnly: Story = {
  args: {
    layout: 'grid',
    showLabels: false,
    loading: false,
    disabled: false,
  },
};

export const Loading: Story = {
  args: {
    layout: 'vertical',
    showLabels: true,
    loading: true,
    disabled: false,
  },
};

export const Disabled: Story = {
  args: {
    layout: 'vertical',
    showLabels: true,
    loading: false,
    disabled: true,
  },
};

// Story showing selective providers
export const SelectiveProviders: Story = {
  args: {
    layout: 'vertical',
    showLabels: true,
    loading: false,
    disabled: false,
    onGoogleLogin: undefined, // Don't show Google
    onLinkedInLogin: undefined, // Don't show LinkedIn
  },
};

// Story with divider
export const WithDivider: Story = {
  render: args => (
    <div className="w-80 space-y-4">
      <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Email Login Form</h3>
        <div className="space-y-3">
          <input
            type="email"
            placeholder="Email"
            className="w-full p-3 border rounded-lg"
          />
          <input
            type="password"
            placeholder="Password"
            className="w-full p-3 border rounded-lg"
          />
          <button className="w-full p-3 bg-blue-600 text-white rounded-lg">
            Sign In
          </button>
        </div>
      </div>

      <SocialLoginDivider text="or continue with" />

      <SocialLoginButtons {...args} />
    </div>
  ),
  args: {
    layout: 'vertical',
    showLabels: true,
    loading: false,
    disabled: false,
  },
};

// Story showing all layouts side by side
export const AllLayouts: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Vertical Layout</h3>
        <SocialLoginButtons layout="vertical" showLabels />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Horizontal Layout</h3>
        <SocialLoginButtons layout="horizontal" showLabels />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Grid Layout</h3>
        <SocialLoginButtons layout="grid" showLabels />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Grid Icons Only</h3>
        <SocialLoginButtons layout="grid" showLabels={false} />
      </div>
    </div>
  ),
};
