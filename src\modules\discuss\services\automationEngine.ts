// Enhanced automation engine for advanced triggers and workflows
import type { 
  Bot<PERSON><PERSON><PERSON>, 
  Bot<PERSON><PERSON>, 
  BotContext,
  Workflow,
  WorkflowStep,
  AutomationRule,
  EventProcessor,
  MessageTemplate,
  SmartResponseEngine,
  Message,
  User,
  Channel
} from '../types';
import { websocketService } from './websocketService';
import { messageService } from './messageService';
import { botFramework } from './botFramework';

export interface AutomationContext {
  message?: Message;
  user?: User;
  channel?: Channel;
  event?: any;
  variables?: Record<string, any>;
  timestamp: Date;
  executionId: string;
}

export interface TriggerEvaluationResult {
  triggered: boolean;
  confidence: number;
  matchedPatterns: string[];
  extractedData: Record<string, any>;
  context: AutomationContext;
}

export class AutomationEngine {
  private eventProcessors: Map<string, EventProcessor> = new Map();
  private workflows: Map<string, Workflow> = new Map();
  private responseEngines: Map<string, SmartResponseEngine> = new Map();
  private templates: Map<string, MessageTemplate> = new Map();
  private executionQueue: AutomationExecution[] = [];
  private isProcessing = false;

  constructor() {
    this.initializeEngine();
  }

  private initializeEngine(): void {
    // Listen for various events
    websocketService.on('message_created', this.handleMessageEvent.bind(this));
    websocketService.on('user_joined', this.handleUserEvent.bind(this));
    websocketService.on('user_left', this.handleUserEvent.bind(this));
    websocketService.on('message_updated', this.handleMessageEvent.bind(this));
    websocketService.on('user_typing', this.handleTypingEvent.bind(this));
    
    // Start processing queue
    this.startProcessingQueue();
  }

  // Enhanced trigger evaluation with advanced pattern matching
  async evaluateTrigger(trigger: BotTrigger, context: AutomationContext): Promise<TriggerEvaluationResult> {
    const result: TriggerEvaluationResult = {
      triggered: false,
      confidence: 0,
      matchedPatterns: [],
      extractedData: {},
      context
    };

    try {
      switch (trigger.type) {
        case 'keyword':
          return await this.evaluateKeywordTrigger(trigger, context, result);
        case 'regex':
          return await this.evaluateRegexTrigger(trigger, context, result);
        case 'sentiment':
          return await this.evaluateSentimentTrigger(trigger, context, result);
        case 'context':
          return await this.evaluateContextTrigger(trigger, context, result);
        case 'user_action':
          return await this.evaluateUserActionTrigger(trigger, context, result);
        case 'time_based':
          return await this.evaluateTimeBasedTrigger(trigger, context, result);
        case 'reaction':
          return await this.evaluateReactionTrigger(trigger, context, result);
        default:
          // Fallback to basic pattern matching
          return await this.evaluateBasicTrigger(trigger, context, result);
      }
    } catch (error) {
      console.error('Error evaluating trigger:', error);
      return result;
    }
  }

  // Keyword trigger with fuzzy matching and context awareness
  private async evaluateKeywordTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (!context.message?.content) return result;

    const config = trigger.config;
    const content = config?.caseSensitive ? context.message.content : context.message.content.toLowerCase();
    const keywords = config?.keywords || [trigger.pattern];

    for (const keyword of keywords) {
      const searchKeyword = config?.caseSensitive ? keyword : keyword.toLowerCase();
      
      if (config?.wholeWordsOnly) {
        const regex = new RegExp(`\\b${this.escapeRegex(searchKeyword)}\\b`, config?.caseSensitive ? 'g' : 'gi');
        if (regex.test(content)) {
          result.triggered = true;
          result.confidence = 1.0;
          result.matchedPatterns.push(keyword);
        }
      } else if (config?.fuzzyMatching) {
        const similarity = this.calculateSimilarity(content, searchKeyword);
        if (similarity >= (config?.fuzzyThreshold || 0.8)) {
          result.triggered = true;
          result.confidence = similarity;
          result.matchedPatterns.push(keyword);
        }
      } else if (content.includes(searchKeyword)) {
        result.triggered = true;
        result.confidence = 1.0;
        result.matchedPatterns.push(keyword);
      }
    }

    return result;
  }

  // Regex trigger with advanced flags and multiline support
  private async evaluateRegexTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (!context.message?.content) return result;

    try {
      const flags = trigger.config?.regexFlags || 'gi';
      const regex = new RegExp(trigger.pattern, flags);
      const matches = context.message.content.match(regex);

      if (matches) {
        result.triggered = true;
        result.confidence = 1.0;
        result.matchedPatterns = matches;
        result.extractedData.regexMatches = matches;
      }
    } catch (error) {
      console.error('Invalid regex pattern:', trigger.pattern, error);
    }

    return result;
  }

  // Sentiment analysis trigger
  private async evaluateSentimentTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (!context.message?.content) return result;

    // Simulate sentiment analysis (in real implementation, use ML service)
    const sentiment = await this.analyzeSentiment(context.message.content);
    const config = trigger.config;

    if (config?.sentimentType) {
      if (sentiment.type === config.sentimentType) {
        result.triggered = true;
        result.confidence = sentiment.confidence;
        result.extractedData.sentiment = sentiment;
      }
    } else if (config?.sentimentThreshold !== undefined) {
      if (sentiment.score >= config.sentimentThreshold) {
        result.triggered = true;
        result.confidence = sentiment.confidence;
        result.extractedData.sentiment = sentiment;
      }
    }

    return result;
  }

  // Context-aware trigger that analyzes conversation history
  private async evaluateContextTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (!context.message || !context.channel) return result;

    const config = trigger.config;
    const contextWindow = config?.contextWindow || 5;

    // Get recent messages for context
    const recentMessages = await this.getRecentMessages(context.channel.id, contextWindow);
    const contextText = recentMessages.map(m => m.content).join(' ');

    // Check context keywords
    if (config?.contextKeywords) {
      for (const keyword of config.contextKeywords) {
        if (contextText.toLowerCase().includes(keyword.toLowerCase())) {
          result.triggered = true;
          result.confidence = 0.8;
          result.matchedPatterns.push(keyword);
          result.extractedData.contextMatches = config.contextKeywords.filter(k => 
            contextText.toLowerCase().includes(k.toLowerCase())
          );
        }
      }
    }

    return result;
  }

  // User action trigger for various user activities
  private async evaluateUserActionTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    const config = trigger.config;
    const actionTypes = config?.actionTypes || [];

    // Check if the current event matches any of the configured action types
    if (context.event && actionTypes.includes(context.event.type)) {
      result.triggered = true;
      result.confidence = 1.0;
      result.extractedData.actionType = context.event.type;
      result.extractedData.actionData = context.event.data;
    }

    return result;
  }

  // Time-based trigger for specific time windows
  private async evaluateTimeBasedTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    const config = trigger.config;
    const timeWindow = config?.timeWindow;

    if (!timeWindow) return result;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const startTime = this.parseTime(timeWindow.start);
    const endTime = this.parseTime(timeWindow.end);

    // Check if current time is within the window
    let inTimeWindow = false;
    if (startTime <= endTime) {
      inTimeWindow = currentTime >= startTime && currentTime <= endTime;
    } else {
      // Handle overnight windows (e.g., 22:00 to 06:00)
      inTimeWindow = currentTime >= startTime || currentTime <= endTime;
    }

    // Check day of week if specified
    if (inTimeWindow && timeWindow.daysOfWeek) {
      const currentDay = now.getDay();
      inTimeWindow = timeWindow.daysOfWeek.includes(currentDay);
    }

    if (inTimeWindow) {
      result.triggered = true;
      result.confidence = 1.0;
      result.extractedData.timeWindow = timeWindow;
    }

    return result;
  }

  // Reaction trigger for message reactions
  private async evaluateReactionTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (context.event?.type !== 'reaction_add') return result;

    const emoji = context.event.data?.emoji;
    if (emoji && trigger.pattern === emoji) {
      result.triggered = true;
      result.confidence = 1.0;
      result.extractedData.emoji = emoji;
      result.extractedData.messageId = context.event.data?.messageId;
    }

    return result;
  }

  // Basic trigger evaluation for backward compatibility
  private async evaluateBasicTrigger(
    trigger: BotTrigger, 
    context: AutomationContext, 
    result: TriggerEvaluationResult
  ): Promise<TriggerEvaluationResult> {
    if (!context.message?.content) return result;

    const content = context.message.content.toLowerCase();
    const pattern = trigger.pattern.toLowerCase();

    if (content.includes(pattern)) {
      result.triggered = true;
      result.confidence = 1.0;
      result.matchedPatterns.push(trigger.pattern);
    }

    return result;
  }

  // Helper methods
  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private async analyzeSentiment(text: string): Promise<{ type: string; score: number; confidence: number }> {
    // Simplified sentiment analysis (in real implementation, use ML service)
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'horrible', 'worst'];
    
    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;
    
    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });
    
    const total = positiveCount + negativeCount;
    if (total === 0) {
      return { type: 'neutral', score: 0, confidence: 0.5 };
    }
    
    const score = (positiveCount - negativeCount) / total;
    const type = score > 0.1 ? 'positive' : score < -0.1 ? 'negative' : 'neutral';
    const confidence = Math.abs(score);
    
    return { type, score, confidence };
  }

  private async getRecentMessages(channelId: string, count: number): Promise<Message[]> {
    try {
      const response = await messageService.getChannelMessages(channelId, 1, count);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching recent messages:', error);
      return [];
    }
  }

  private parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Event handlers
  private async handleMessageEvent(event: any): Promise<void> {
    const context: AutomationContext = {
      message: event.message,
      user: event.user,
      channel: event.channel,
      event,
      timestamp: new Date(),
      executionId: this.generateExecutionId()
    };

    await this.processEventProcessors('message', context);
  }

  private async handleUserEvent(event: any): Promise<void> {
    const context: AutomationContext = {
      user: event.user,
      channel: event.channel,
      event,
      timestamp: new Date(),
      executionId: this.generateExecutionId()
    };

    await this.processEventProcessors('user', context);
  }

  private async handleTypingEvent(event: any): Promise<void> {
    const context: AutomationContext = {
      user: event.user,
      channel: event.channel,
      event,
      timestamp: new Date(),
      executionId: this.generateExecutionId()
    };

    await this.processEventProcessors('typing', context);
  }

  private async processEventProcessors(eventType: string, context: AutomationContext): Promise<void> {
    for (const [id, processor] of this.eventProcessors) {
      if (!processor.enabled || !processor.eventTypes.includes(eventType)) continue;

      try {
        const shouldProcess = await this.evaluateEventFilters(processor.filters, context);
        if (shouldProcess) {
          await this.executeEventActions(processor.actions, context);
        }
      } catch (error) {
        console.error(`Error processing event with processor ${id}:`, error);
      }
    }
  }

  private async evaluateEventFilters(filters: any[], context: AutomationContext): Promise<boolean> {
    // Implement filter evaluation logic
    return true; // Simplified for now
  }

  private async executeEventActions(actions: any[], context: AutomationContext): Promise<void> {
    // Implement action execution logic
    console.log('Executing event actions:', actions);
  }

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private startProcessingQueue(): void {
    setInterval(() => {
      if (!this.isProcessing && this.executionQueue.length > 0) {
        this.processNextExecution();
      }
    }, 100); // Process every 100ms
  }

  private async processNextExecution(): Promise<void> {
    if (this.executionQueue.length === 0) return;

    this.isProcessing = true;
    const execution = this.executionQueue.shift()!;

    try {
      await this.executeAutomation(execution);
    } catch (error) {
      console.error('Error executing automation:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async executeAutomation(execution: AutomationExecution): Promise<void> {
    // Implement automation execution logic
    console.log('Executing automation:', execution);
  }
}

interface AutomationExecution {
  id: string;
  type: 'trigger' | 'workflow' | 'scheduled';
  data: any;
  context: AutomationContext;
  priority: number;
  scheduledFor?: Date;
}

// Export singleton instance
export const automationEngine = new AutomationEngine();
