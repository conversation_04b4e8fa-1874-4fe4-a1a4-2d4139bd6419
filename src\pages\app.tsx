import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { DynamicAppView } from '../components/DynamicAppView';
import { DiscussModule } from '../modules/discuss';

const AppPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const menuId = searchParams.get('menu');

  // Use DiscussModule for discuss app (menu ID 10)
  if (menuId === '10') {
    return <DiscussModule data-testid="discuss-module" />;
  }

  // Use DynamicAppView for all other apps
  return <DynamicAppView data-testid="app-page" />;
};

export default AppPage;
