import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MessageSearch } from '../../../components/ui/MessageSearch';
import type { SearchResult, SearchFilters } from '../../../components/ui/MessageSearch';

// Mock the theme store
vi.mock('../../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      backgroundSecondary: '#f1f5f9',
      error: '#ef4444',
    },
  }),
}));

describe('MessageSearch Component', () => {
  const mockSearchResults: SearchResult[] = [
    {
      message: {
        id: 'msg-1',
        content: 'This is a test message about the project',
        authorId: '1',
        channelId: 'general',
        timestamp: new Date('2024-01-10T10:30:00'),
        reactions: [],
        attachments: [],
        mentions: [],
        isDeleted: false,
        deliveryStatus: 'read',
      },
      author: {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'JD',
        status: 'online',
        lastSeen: new Date(),
      },
      channel: {
        id: 'general',
        name: 'general',
        type: 'public',
        description: 'General discussion',
        memberIds: ['1', '2'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      snippet: 'This is a test message about the project',
      highlights: [10, 15],
      score: 0.9,
    },
  ];

  const mockOnSearch = vi.fn().mockResolvedValue(mockSearchResults);
  const mockOnResultClick = vi.fn();

  const defaultProps = {
    onSearch: mockOnSearch,
    onResultClick: mockOnResultClick,
    placeholder: 'Search messages...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders search input correctly', () => {
    render(<MessageSearch {...defaultProps} />);
    
    expect(screen.getByPlaceholderText('Search messages...')).toBeInTheDocument();
    expect(screen.getByTitle('Search')).toBeInTheDocument();
  });

  it('handles search input', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test query');
    
    expect(searchInput).toHaveValue('test query');
  });

  it('performs search on input change with debounce', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('test', {});
    }, { timeout: 1000 });
  });

  it('performs search on Enter key', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test query');
    await user.keyboard('{Enter}');
    
    expect(mockOnSearch).toHaveBeenCalledWith('test query', {});
  });

  it('performs search on search button click', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test query');
    
    const searchButton = screen.getByTitle('Search');
    await user.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith('test query', {});
  });

  it('displays search results', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('This is a test message about the project')).toBeInTheDocument();
      expect(screen.getByText('#general')).toBeInTheDocument();
    });
  });

  it('handles result click', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      const resultItem = screen.getByText('This is a test message about the project').closest('div');
      fireEvent.click(resultItem!);
      
      expect(mockOnResultClick).toHaveBeenCalledWith(mockSearchResults[0]);
    });
  });

  it('shows no results message when search returns empty', async () => {
    const emptySearchMock = vi.fn().mockResolvedValue([]);
    const user = userEvent.setup();
    
    render(<MessageSearch {...defaultProps} onSearch={emptySearchMock} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'nonexistent');
    
    await waitFor(() => {
      expect(screen.getByText('No results found')).toBeInTheDocument();
      expect(screen.getByText('Try different keywords or adjust your filters')).toBeInTheDocument();
    });
  });

  it('shows loading state during search', async () => {
    const slowSearchMock = vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockSearchResults), 1000))
    );
    const user = userEvent.setup();
    
    render(<MessageSearch {...defaultProps} onSearch={slowSearchMock} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    expect(screen.getByText('Searching...')).toBeInTheDocument();
  });

  it('shows advanced filters when filter button is clicked', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showAdvancedFilters={true} />);
    
    const filterButton = screen.getByTitle('Advanced filters');
    await user.click(filterButton);
    
    expect(screen.getByText('Advanced Filters')).toBeInTheDocument();
    expect(screen.getByLabelText('Channels')).toBeInTheDocument();
    expect(screen.getByLabelText('Authors')).toBeInTheDocument();
    expect(screen.getByLabelText('Date Range')).toBeInTheDocument();
  });

  it('applies advanced filters to search', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showAdvancedFilters={true} />);
    
    // Open filters
    const filterButton = screen.getByTitle('Advanced filters');
    await user.click(filterButton);
    
    // Set filters
    const channelFilter = screen.getByLabelText('Channels');
    await user.type(channelFilter, 'general');
    
    const hasAttachmentsFilter = screen.getByLabelText('Has attachments');
    await user.click(hasAttachmentsFilter);
    
    // Apply filters
    const applyButton = screen.getByText('Apply Filters');
    await user.click(applyButton);
    
    // Search with filters
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('test', {
        channels: ['general'],
        hasAttachments: true,
      });
    });
  });

  it('clears search results when input is cleared', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    await user.clear(searchInput);
    
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  it('shows search suggestions', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showSuggestions={true} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'proj');
    
    await waitFor(() => {
      expect(screen.getByText('project')).toBeInTheDocument();
      expect(screen.getByText('@john')).toBeInTheDocument();
      expect(screen.getByText('#general')).toBeInTheDocument();
    });
  });

  it('handles search suggestion click', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showSuggestions={true} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'proj');
    
    await waitFor(() => {
      const suggestion = screen.getByText('project');
      fireEvent.click(suggestion);
      
      expect(searchInput).toHaveValue('project');
    });
  });

  it('shows recent searches', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showRecentSearches={true} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.click(searchInput);
    
    expect(screen.getByText('Recent Searches')).toBeInTheDocument();
    expect(screen.getByText('project update')).toBeInTheDocument();
    expect(screen.getByText('meeting notes')).toBeInTheDocument();
  });

  it('handles recent search click', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showRecentSearches={true} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.click(searchInput);
    
    const recentSearch = screen.getByText('project update');
    fireEvent.click(recentSearch);
    
    expect(searchInput).toHaveValue('project update');
    expect(mockOnSearch).toHaveBeenCalledWith('project update', {});
  });

  it('clears recent searches', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} showRecentSearches={true} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.click(searchInput);
    
    const clearButton = screen.getByText('Clear All');
    await user.click(clearButton);
    
    expect(screen.queryByText('project update')).not.toBeInTheDocument();
  });

  it('handles keyboard navigation in results', async () => {
    const user = userEvent.setup();
    render(<MessageSearch {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    // Navigate with arrow keys
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');
    
    expect(mockOnResultClick).toHaveBeenCalledWith(mockSearchResults[0]);
  });

  it('closes results when clicking outside', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <MessageSearch {...defaultProps} />
        <div data-testid="outside">Outside</div>
      </div>
    );
    
    const searchInput = screen.getByPlaceholderText('Search messages...');
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const outside = screen.getByTestId('outside');
    await user.click(outside);
    
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });
});
