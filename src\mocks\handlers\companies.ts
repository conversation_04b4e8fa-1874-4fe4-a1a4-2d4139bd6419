// Company management handlers for MSW
import { http, HttpResponse } from 'msw';
import {
  companies,
  getCompanyById,
  getActiveCompanies,
  searchCompanies,
  type Company,
} from '../data/companies';
import { getUserById, type User } from '../data/users';
import {
  delay,
  randomDelay,
  simulateError,
  commonErrors,
  successResponse,
  errorResponse,
  notFoundResponse,
  unauthorizedResponse,
  extractBearerToken,
  validateToken,
  paginate,
  fuzzySearch,
  sortData,
  filterData,
  parseQueryParams,
  generateId,
  generateTimestamp,
  type SortDirection,
  type FilterCondition,
} from '../utils';

// Mutable copy of companies for CRUD operations
const mutableCompanies = [...companies];

// Helper to check permissions
const hasPermission = (user: User, permission: string): boolean => {
  return (
    user.permissions.includes(permission) ||
    user.permissions.includes('admin') ||
    user.permissions.includes('manage_apps')
  );
};

// Helper to get authenticated user
const getAuthenticatedUser = async (
  authHeader: string | null
): Promise<User | null> => {
  const token = extractBearerToken(authHeader);
  if (!token) return null;

  const userId = validateToken(token);
  if (!userId) return null;

  return getUserById(userId) || null;
};

export const companyHandlers = [
  // Get all companies with pagination, search, and filtering
  http.get('/api/companies', async ({ request }) => {
    await randomDelay(200, 600);

    // Simulate occasional errors
    const error = simulateError([commonErrors.serverError]);
    if (error) return error;

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const params = parseQueryParams(url);

    let filteredCompanies = [...mutableCompanies];

    // Apply search
    if (params.search) {
      filteredCompanies = fuzzySearch(filteredCompanies, params.search, [
        'name',
        'domain',
        'description',
        'industry',
      ]);
    }

    // Apply filters
    const filters: FilterCondition<Company>[] = [];
    if (params.isActive !== undefined) {
      filters.push({
        field: 'isActive',
        operator: 'eq',
        value: params.isActive,
      });
    }
    if (params.industry) {
      filters.push({
        field: 'industry',
        operator: 'eq',
        value: params.industry,
      });
    }
    if (params.size) {
      filters.push({ field: 'size', operator: 'eq', value: params.size });
    }
    if (params.country) {
      filters.push({
        field: 'location',
        operator: 'contains',
        value: params.country,
      });
    }

    if (filters.length > 0) {
      filteredCompanies = filterData(filteredCompanies, filters);
    }

    // Apply sorting
    if (params.sortBy) {
      const direction: SortDirection =
        params.sortOrder === 'desc' ? 'desc' : 'asc';
      filteredCompanies = sortData(
        filteredCompanies,
        params.sortBy as keyof Company,
        direction
      );
    }

    // Apply pagination
    const paginatedResult = paginate(filteredCompanies, {
      page: params.page,
      limit: params.limit,
    });

    return successResponse(paginatedResult);
  }),

  // Get active companies only
  http.get('/api/companies/active', async ({ request }) => {
    await delay(200);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const activeCompanies = mutableCompanies.filter(
      company => company.isActive
    );
    return successResponse(activeCompanies);
  }),

  // Get single company by ID
  http.get('/api/companies/:id', async ({ params, request }) => {
    await delay(150);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const company = mutableCompanies.find(c => c.id === params.id);
    if (!company) {
      return notFoundResponse('Company');
    }

    return successResponse(company);
  }),

  // Create new company
  http.post('/api/companies', async ({ request }) => {
    await randomDelay(800, 1200);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (
      !hasPermission(currentUser, 'write') &&
      !hasPermission(currentUser, 'manage_apps')
    ) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    try {
      const companyData = (await request.json()) as Partial<Company>;

      // Validate required fields
      if (!companyData.name || !companyData.domain) {
        return errorResponse('Name and domain are required', 400);
      }

      // Check if domain already exists
      if (mutableCompanies.some(c => c.domain === companyData.domain)) {
        return errorResponse('Domain already exists', 409);
      }

      const newCompany: Company = {
        id: generateId(),
        name: companyData.name,
        logo: companyData.logo || '🏢',
        domain: companyData.domain,
        isActive: companyData.isActive !== false,
        description: companyData.description,
        industry: companyData.industry,
        size: companyData.size || 'medium',
        createdAt: generateTimestamp(),
        updatedAt: generateTimestamp(),
        settings: companyData.settings || {
          currency: 'USD',
          timezone: 'UTC',
          language: 'en',
          dateFormat: 'MM/dd/yyyy',
        },
        location: companyData.location,
        contact: companyData.contact,
      };

      mutableCompanies.push(newCompany);

      return successResponse(newCompany, 201);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Update company
  http.put('/api/companies/:id', async ({ params, request }) => {
    await delay(600);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (
      !hasPermission(currentUser, 'write') &&
      !hasPermission(currentUser, 'manage_apps')
    ) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const companyIndex = mutableCompanies.findIndex(c => c.id === params.id);
    if (companyIndex === -1) {
      return notFoundResponse('Company');
    }

    try {
      const updateData = (await request.json()) as Partial<Company>;
      const existingCompany = mutableCompanies[companyIndex];

      // Prevent domain conflicts
      if (updateData.domain && updateData.domain !== existingCompany.domain) {
        if (
          mutableCompanies.some(
            c => c.domain === updateData.domain && c.id !== existingCompany.id
          )
        ) {
          return errorResponse('Domain already exists', 409);
        }
      }

      const updatedCompany: Company = {
        ...existingCompany,
        ...updateData,
        id: existingCompany.id, // Prevent ID changes
        createdAt: existingCompany.createdAt, // Prevent creation date changes
        updatedAt: generateTimestamp(),
      };

      mutableCompanies[companyIndex] = updatedCompany;

      return successResponse(updatedCompany);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Delete company
  http.delete('/api/companies/:id', async ({ params, request }) => {
    await delay(400);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (
      !hasPermission(currentUser, 'delete') &&
      !hasPermission(currentUser, 'manage_apps')
    ) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const companyIndex = mutableCompanies.findIndex(c => c.id === params.id);
    if (companyIndex === -1) {
      return notFoundResponse('Company');
    }

    mutableCompanies.splice(companyIndex, 1);

    return successResponse({ message: 'Company deleted successfully' });
  }),

  // Get company statistics
  http.get('/api/companies/stats', async ({ request }) => {
    await delay(300);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const stats = {
      total: mutableCompanies.length,
      active: mutableCompanies.filter(c => c.isActive).length,
      inactive: mutableCompanies.filter(c => !c.isActive).length,
      byIndustry: mutableCompanies.reduce(
        (acc, company) => {
          if (company.industry) {
            acc[company.industry] = (acc[company.industry] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      ),
      bySize: mutableCompanies.reduce(
        (acc, company) => {
          if (company.size) {
            acc[company.size] = (acc[company.size] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      ),
    };

    return successResponse(stats);
  }),
];
