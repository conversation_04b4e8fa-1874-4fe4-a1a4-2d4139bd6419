import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useMessages } from '../../hooks/useMessages';
import type { Message } from '../../types';

// Mock the message service
const mockMessageService = {
  getMessages: vi.fn(),
  sendMessage: vi.fn(),
  editMessage: vi.fn(),
  deleteMessage: vi.fn(),
  addReaction: vi.fn(),
  removeReaction: vi.fn(),
};

vi.mock('../../services/messageService', () => ({
  messageService: mockMessageService,
}));

// Mock WebSocket hook
const mockWebSocketHook = {
  isConnected: true,
  sendMessage: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
};

vi.mock('../../hooks/useWebSocket', () => ({
  useWebSocket: () => mockWebSocketHook,
}));

describe('useMessages Hook', () => {
  const mockMessages: Message[] = [
    {
      id: 'msg-1',
      content: 'Hello world!',
      authorId: '1',
      channelId: 'general',
      timestamp: new Date('2024-01-10T10:30:00'),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read',
    },
    {
      id: 'msg-2',
      content: 'How is everyone?',
      authorId: '2',
      channelId: 'general',
      timestamp: new Date('2024-01-10T10:31:00'),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockMessageService.getMessages.mockResolvedValue({
      success: true,
      data: {
        messages: mockMessages,
        hasMore: false,
        total: 2,
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('initializes with default state', () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    expect(result.current.messages).toEqual([]);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.hasMore).toBe(false);
  });

  it('loads messages on mount', async () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockMessageService.getMessages).toHaveBeenCalledWith('general', {
      page: 1,
      pageSize: 50,
    });
    expect(result.current.messages).toEqual(mockMessages);
    expect(result.current.error).toBe(null);
  });

  it('handles loading error', async () => {
    const errorMessage = 'Failed to load messages';
    mockMessageService.getMessages.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.messages).toEqual([]);
  });

  it('sends new message', async () => {
    mockMessageService.sendMessage.mockResolvedValue({
      success: true,
      data: {
        id: 'msg-3',
        content: 'New message',
        authorId: '1',
        channelId: 'general',
        timestamp: new Date(),
        reactions: [],
        attachments: [],
        mentions: [],
        isDeleted: false,
        deliveryStatus: 'sent',
      },
    });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.sendMessage({
        content: 'New message',
        attachments: [],
        mentions: [],
      });
    });

    expect(mockMessageService.sendMessage).toHaveBeenCalledWith('general', {
      content: 'New message',
      attachments: [],
      mentions: [],
    });
  });

  it('handles send message error', async () => {
    mockMessageService.sendMessage.mockRejectedValue(new Error('Send failed'));

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.sendMessage({
        content: 'New message',
        attachments: [],
        mentions: [],
      });
    });

    expect(result.current.error).toBe('Send failed');
  });

  it('edits existing message', async () => {
    const editedMessage = {
      ...mockMessages[0],
      content: 'Edited message',
      editedAt: new Date(),
    };

    mockMessageService.editMessage.mockResolvedValue({
      success: true,
      data: editedMessage,
    });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.editMessage('msg-1', 'Edited message');
    });

    expect(mockMessageService.editMessage).toHaveBeenCalledWith('msg-1', 'Edited message');
  });

  it('deletes message', async () => {
    mockMessageService.deleteMessage.mockResolvedValue({
      success: true,
      data: { id: 'msg-1' },
    });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.deleteMessage('msg-1');
    });

    expect(mockMessageService.deleteMessage).toHaveBeenCalledWith('msg-1');
  });

  it('adds reaction to message', async () => {
    mockMessageService.addReaction.mockResolvedValue({
      success: true,
      data: { messageId: 'msg-1', emoji: '👍', userId: '1' },
    });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.addReaction('msg-1', '👍');
    });

    expect(mockMessageService.addReaction).toHaveBeenCalledWith('msg-1', '👍');
  });

  it('removes reaction from message', async () => {
    mockMessageService.removeReaction.mockResolvedValue({
      success: true,
      data: { messageId: 'msg-1', emoji: '👍', userId: '1' },
    });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.removeReaction('msg-1', '👍');
    });

    expect(mockMessageService.removeReaction).toHaveBeenCalledWith('msg-1', '👍');
  });

  it('loads more messages', async () => {
    const additionalMessages: Message[] = [
      {
        id: 'msg-3',
        content: 'Older message',
        authorId: '1',
        channelId: 'general',
        timestamp: new Date('2024-01-10T10:29:00'),
        reactions: [],
        attachments: [],
        mentions: [],
        isDeleted: false,
        deliveryStatus: 'read',
      },
    ];

    // First call returns hasMore: true
    mockMessageService.getMessages
      .mockResolvedValueOnce({
        success: true,
        data: {
          messages: mockMessages,
          hasMore: true,
          total: 3,
        },
      })
      .mockResolvedValueOnce({
        success: true,
        data: {
          messages: additionalMessages,
          hasMore: false,
          total: 3,
        },
      });

    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.hasMore).toBe(true);

    await act(async () => {
      await result.current.loadMore();
    });

    expect(mockMessageService.getMessages).toHaveBeenCalledTimes(2);
    expect(mockMessageService.getMessages).toHaveBeenLastCalledWith('general', {
      page: 2,
      pageSize: 50,
    });
  });

  it('refreshes messages', async () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.refresh();
    });

    expect(mockMessageService.getMessages).toHaveBeenCalledTimes(2);
  });

  it('handles real-time message updates', async () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Simulate WebSocket subscription callback
    const subscribeCall = mockWebSocketHook.subscribe.mock.calls.find(
      call => call[0] === 'message:new'
    );
    expect(subscribeCall).toBeDefined();

    const newMessage: Message = {
      id: 'msg-new',
      content: 'Real-time message',
      authorId: '2',
      channelId: 'general',
      timestamp: new Date(),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'sent',
    };

    act(() => {
      subscribeCall[1](newMessage);
    });

    expect(result.current.messages).toContainEqual(newMessage);
  });

  it('handles real-time message edits', async () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const subscribeCall = mockWebSocketHook.subscribe.mock.calls.find(
      call => call[0] === 'message:edited'
    );
    expect(subscribeCall).toBeDefined();

    const editedMessage = {
      ...mockMessages[0],
      content: 'Edited via WebSocket',
      editedAt: new Date(),
    };

    act(() => {
      subscribeCall[1](editedMessage);
    });

    const updatedMessage = result.current.messages.find(m => m.id === 'msg-1');
    expect(updatedMessage?.content).toBe('Edited via WebSocket');
  });

  it('handles real-time message deletions', async () => {
    const { result } = renderHook(() => useMessages({ channelId: 'general' }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const subscribeCall = mockWebSocketHook.subscribe.mock.calls.find(
      call => call[0] === 'message:deleted'
    );
    expect(subscribeCall).toBeDefined();

    act(() => {
      subscribeCall[1]({ id: 'msg-1' });
    });

    expect(result.current.messages.find(m => m.id === 'msg-1')).toBeUndefined();
  });

  it('unsubscribes from WebSocket events on unmount', () => {
    const { unmount } = renderHook(() => useMessages({ channelId: 'general' }));

    unmount();

    expect(mockWebSocketHook.unsubscribe).toHaveBeenCalledWith('message:new');
    expect(mockWebSocketHook.unsubscribe).toHaveBeenCalledWith('message:edited');
    expect(mockWebSocketHook.unsubscribe).toHaveBeenCalledWith('message:deleted');
    expect(mockWebSocketHook.unsubscribe).toHaveBeenCalledWith('message:reaction');
  });

  it('reloads messages when channelId changes', async () => {
    const { result, rerender } = renderHook(
      ({ channelId }) => useMessages({ channelId }),
      { initialProps: { channelId: 'general' } }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockMessageService.getMessages).toHaveBeenCalledWith('general', expect.any(Object));

    rerender({ channelId: 'random' });

    await waitFor(() => {
      expect(mockMessageService.getMessages).toHaveBeenCalledWith('random', expect.any(Object));
    });
  });
});
