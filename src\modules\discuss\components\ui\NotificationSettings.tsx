import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { notificationService } from '../../services/notificationService';
import type { NotificationSettings } from '../../types';

export interface NotificationSettingsProps {
  onSettingsChange?: (settings: NotificationSettings) => void;
  className?: string;
  'data-testid'?: string;
}

export const NotificationSettingsPanel: React.FC<NotificationSettingsProps> = ({
  onSettingsChange,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [settings, setSettings] = useState<NotificationSettings>({
    desktop: true,
    sound: true,
    mentions: true,
    directMessages: true,
    channelMessages: false,
    reactions: true,
    emailDigest: false,
    mobileNotifications: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
    },
  });
  const [isLoading, setIsLoading] = useState(true);
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');

  // Load settings on mount
  useEffect(() => {
    loadSettings();
    checkPermissionStatus();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await notificationService.getNotificationSettings();
      if (response.success && response.data) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Failed to load notification settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermissionStatus = () => {
    if (notificationService.isNotificationSupported()) {
      setPermissionStatus(Notification.permission);
    }
  };

  const handleSettingChange = async (key: keyof NotificationSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    
    try {
      const response = await notificationService.updateNotificationSettings({ [key]: value });
      if (response.success && response.data) {
        setSettings(response.data);
        onSettingsChange?.(response.data);
      }
    } catch (error) {
      console.error('Failed to update notification settings:', error);
      // Revert on error
      setSettings(settings);
    }
  };

  const handleQuietHoursChange = async (key: 'enabled' | 'start' | 'end', value: any) => {
    const newQuietHours = { ...settings.quietHours, [key]: value };
    const newSettings = { ...settings, quietHours: newQuietHours };
    setSettings(newSettings);
    
    try {
      const response = await notificationService.updateNotificationSettings({ quietHours: newQuietHours });
      if (response.success && response.data) {
        setSettings(response.data);
        onSettingsChange?.(response.data);
      }
    } catch (error) {
      console.error('Failed to update quiet hours:', error);
      setSettings(settings);
    }
  };

  const requestNotificationPermission = async () => {
    try {
      const permission = await notificationService.requestPermission();
      setPermissionStatus(permission);
    } catch (error) {
      console.error('Failed to request notification permission:', error);
    }
  };

  const testNotification = async () => {
    try {
      await notificationService.testNotification();
    } catch (error) {
      console.error('Failed to send test notification:', error);
    }
  };

  if (isLoading) {
    return (
      <div className={`p-4 ${className}`} data-testid={testId}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`} data-testid={testId}>
      {/* Permission Status */}
      {notificationService.isNotificationSupported() && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
            Desktop Notifications
          </h3>
          
          {permissionStatus === 'default' && (
            <div className="p-4 rounded-lg" style={{ backgroundColor: colors.backgroundSecondary }}>
              <p className="text-sm mb-3" style={{ color: colors.text }}>
                Enable desktop notifications to stay updated on messages and mentions.
              </p>
              <button
                onClick={requestNotificationPermission}
                className="px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity"
                style={{ backgroundColor: colors.primary }}
              >
                Enable Notifications
              </button>
            </div>
          )}
          
          {permissionStatus === 'denied' && (
            <div className="p-4 rounded-lg" style={{ backgroundColor: `${colors.error}20` }}>
              <p className="text-sm" style={{ color: colors.error }}>
                Notifications are blocked. Please enable them in your browser settings.
              </p>
            </div>
          )}
          
          {permissionStatus === 'granted' && (
            <div className="p-4 rounded-lg" style={{ backgroundColor: `${colors.success || colors.primary}20` }}>
              <div className="flex items-center justify-between">
                <p className="text-sm" style={{ color: colors.success || colors.primary }}>
                  ✓ Notifications are enabled
                </p>
                <button
                  onClick={testNotification}
                  className="text-sm px-3 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  style={{ color: colors.primary }}
                >
                  Test
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Notification Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
          Notification Preferences
        </h3>

        {/* Desktop Notifications */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Desktop Notifications
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Show desktop notifications for new messages
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.desktop}
              onChange={(e) => handleSettingChange('desktop', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Sound Notifications */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Sound Notifications
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Play sound when receiving notifications
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.sound}
              onChange={(e) => handleSettingChange('sound', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Mentions */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Mentions
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Notify when someone mentions you
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.mentions}
              onChange={(e) => handleSettingChange('mentions', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Direct Messages */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Direct Messages
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Notify for all direct messages
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.directMessages}
              onChange={(e) => handleSettingChange('directMessages', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Channel Messages */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              All Channel Messages
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Notify for all messages in channels (can be noisy)
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.channelMessages}
              onChange={(e) => handleSettingChange('channelMessages', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {/* Reactions */}
        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Reactions
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Notify when someone reacts to your messages
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.reactions}
              onChange={(e) => handleSettingChange('reactions', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      {/* Quiet Hours */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
          Quiet Hours
        </h3>

        <div className="flex items-center justify-between">
          <div>
            <label className="font-medium" style={{ color: colors.text }}>
              Enable Quiet Hours
            </label>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Disable notifications during specified hours
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.quietHours.enabled}
              onChange={(e) => handleQuietHoursChange('enabled', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {settings.quietHours.enabled && (
          <div className="grid grid-cols-2 gap-4 pl-4">
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>
                Start Time
              </label>
              <input
                type="time"
                value={settings.quietHours.start}
                onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                className="w-full px-3 py-2 border rounded-lg bg-transparent"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>
                End Time
              </label>
              <input
                type="time"
                value={settings.quietHours.end}
                onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                className="w-full px-3 py-2 border rounded-lg bg-transparent"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
