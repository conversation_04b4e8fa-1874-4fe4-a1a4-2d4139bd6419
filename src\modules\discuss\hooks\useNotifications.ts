import { useState, useEffect, useCallback } from 'react';
import { notificationService } from '../services';
import type { Message, User, Channel } from '../types';
import type { NotificationPreferences, NotificationPayload } from '../services/notificationService';

export interface UseNotificationsOptions {
  userId: string;
  autoRequestPermission?: boolean;
}

export interface UseNotificationsReturn {
  isSupported: boolean;
  permission: NotificationPermission;
  preferences: NotificationPreferences | null;
  isLoading: boolean;
  error: string | null;
  requestPermission: () => Promise<NotificationPermission>;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
  notifyNewMessage: (message: Message, author: User, channel: Channel) => Promise<void>;
  notifyReaction: (message: Message, reactor: User, emoji: string, channel: Channel) => Promise<void>;
  notifyMention: (message: Message, author: User, channel: Channel) => Promise<void>;
  showCustomNotification: (payload: NotificationPayload) => Promise<void>;
  clearNotifications: (tag?: string) => void;
}

export const useNotifications = (options: UseNotificationsOptions): UseNotificationsReturn => {
  const { userId, autoRequestPermission = false } = options;
  
  const [isSupported] = useState(() => notificationService.isNotificationSupported());
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check current permission
        if (isSupported) {
          setPermission(Notification.permission);
        }

        // Load user preferences
        const response = await notificationService.getNotificationSettings();
        if (response.success && response.data) {
          setPreferences(response.data);
        }

        // Auto-request permission if enabled
        if (autoRequestPermission && Notification.permission === 'default') {
          await requestPermission();
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize notifications');
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [userId, autoRequestPermission, isSupported]);

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    try {
      const newPermission = await notificationService.requestPermission();
      setPermission(newPermission);
      return newPermission;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to request permission');
      return 'denied';
    }
  }, []);

  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    try {
      setError(null);
      const updatedPreferences = { ...preferences, ...newPreferences } as NotificationPreferences;
      
      const response = await notificationService.updateNotificationSettings(updatedPreferences);
      if (response.success) {
        setPreferences(updatedPreferences);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update preferences');
    }
  }, [preferences]);

  const notifyNewMessage = useCallback(async (message: Message, author: User, channel: Channel) => {
    if (!preferences || !isSupported || permission !== 'granted') return;

    try {
      await notificationService.notifyNewMessage(message, author, channel, preferences);
    } catch (err) {
      console.warn('Failed to show message notification:', err);
    }
  }, [preferences, isSupported, permission]);

  const notifyReaction = useCallback(async (message: Message, reactor: User, emoji: string, channel: Channel) => {
    if (!preferences || !isSupported || permission !== 'granted') return;

    try {
      await notificationService.notifyReaction(message, reactor, emoji, channel, preferences);
    } catch (err) {
      console.warn('Failed to show reaction notification:', err);
    }
  }, [preferences, isSupported, permission]);

  const notifyMention = useCallback(async (message: Message, author: User, channel: Channel) => {
    if (!preferences || !isSupported || permission !== 'granted') return;

    try {
      // Create mention-specific notification
      const payload: NotificationPayload = {
        id: `mention-${message.id}`,
        type: 'mention',
        title: `${author.name} mentioned you`,
        body: `In #${channel.name}: ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}`,
        icon: author.avatar,
        tag: `mention-${message.id}`,
        data: {
          messageId: message.id,
          channelId: channel.id,
          authorId: author.id,
          isMention: true,
        },
        requireInteraction: true,
        timestamp: message.timestamp,
      };

      await notificationService.showNotification(payload);
      
      if (preferences.sound) {
        await notificationService.playNotificationSound(preferences.soundFile);
      }
    } catch (err) {
      console.warn('Failed to show mention notification:', err);
    }
  }, [preferences, isSupported, permission]);

  const showCustomNotification = useCallback(async (payload: NotificationPayload) => {
    if (!isSupported || permission !== 'granted') return;

    try {
      await notificationService.showNotification(payload);
    } catch (err) {
      console.warn('Failed to show custom notification:', err);
    }
  }, [isSupported, permission]);

  const clearNotifications = useCallback((tag?: string) => {
    if (!isSupported) return;

    // Clear notifications by tag or all
    if ('getNotifications' in navigator.serviceWorker) {
      navigator.serviceWorker.getNotifications({ tag }).then(notifications => {
        notifications.forEach(notification => notification.close());
      });
    }
  }, [isSupported]);

  return {
    isSupported,
    permission,
    preferences,
    isLoading,
    error,
    requestPermission,
    updatePreferences,
    notifyNewMessage,
    notifyReaction,
    notifyMention,
    showCustomNotification,
    clearNotifications,
  };
};

// Hook for managing read receipts
export interface UseReadReceiptsOptions {
  userId: string;
  channelId?: string;
}

export interface UseReadReceiptsReturn {
  markAsRead: (messageIds: string[]) => Promise<void>;
  markChannelAsRead: (channelId: string) => Promise<void>;
  getReadStatus: (messageId: string) => Promise<{ isRead: boolean; readBy: User[]; readAt?: Date }>;
  isLoading: boolean;
  error: string | null;
}

export const useReadReceipts = (options: UseReadReceiptsOptions): UseReadReceiptsReturn => {
  const { userId, channelId } = options;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const markAsRead = useCallback(async (messageIds: string[]) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/discuss/messages/read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messageIds, userId }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark messages as read');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark as read');
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  const markChannelAsRead = useCallback(async (channelId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/discuss/channels/${channelId}/read`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark channel as read');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark channel as read');
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  const getReadStatus = useCallback(async (messageId: string) => {
    try {
      const response = await fetch(`/api/discuss/messages/${messageId}/read-status`);
      
      if (!response.ok) {
        throw new Error('Failed to get read status');
      }

      const data = await response.json();
      return data.data;
    } catch (err) {
      console.error('Failed to get read status:', err);
      return { isRead: false, readBy: [] };
    }
  }, []);

  return {
    markAsRead,
    markChannelAsRead,
    getReadStatus,
    isLoading,
    error,
  };
};
