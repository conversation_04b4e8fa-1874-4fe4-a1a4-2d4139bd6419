import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { BotManagement } from '../ui/BotManagement';
import { ArchiveManagement } from '../ui/ArchiveManagement';
import { IntegrationManagement } from '../ui/IntegrationManagement';

export interface AdminViewProps {
  className?: string;
  'data-testid'?: string;
}

export const AdminView: React.FC<AdminViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [activeSection, setActiveSection] = useState<'overview' | 'bots' | 'archive' | 'integrations'>('overview');

  const sections = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📊',
      description: 'System overview and analytics',
    },
    {
      id: 'bots',
      label: 'Bots & Automation',
      icon: '🤖',
      description: 'Manage chatbots and automated workflows',
    },
    {
      id: 'archive',
      label: 'Archive & Compliance',
      icon: '📁',
      description: 'Message archival and compliance management',
    },
    {
      id: 'integrations',
      label: 'Integrations',
      icon: '🔗',
      description: 'External integrations and API management',
    },
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: colors.muted }}>
                Active Bots
              </p>
              <p className="text-2xl font-bold" style={{ color: colors.text }}>
                3
              </p>
            </div>
            <div className="text-3xl">🤖</div>
          </div>
          <p className="text-xs mt-2" style={{ color: colors.success }}>
            +1 this week
          </p>
        </div>

        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: colors.muted }}>
                Archived Messages
              </p>
              <p className="text-2xl font-bold" style={{ color: colors.text }}>
                1,247
              </p>
            </div>
            <div className="text-3xl">📁</div>
          </div>
          <p className="text-xs mt-2" style={{ color: colors.muted }}>
            2.3 GB storage
          </p>
        </div>

        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: colors.muted }}>
                Active Integrations
              </p>
              <p className="text-2xl font-bold" style={{ color: colors.text }}>
                5
              </p>
            </div>
            <div className="text-3xl">🔗</div>
          </div>
          <p className="text-xs mt-2" style={{ color: colors.success }}>
            All operational
          </p>
        </div>

        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: colors.muted }}>
                API Requests
              </p>
              <p className="text-2xl font-bold" style={{ color: colors.text }}>
                12.4K
              </p>
            </div>
            <div className="text-3xl">📈</div>
          </div>
          <p className="text-xs mt-2" style={{ color: colors.success }}>
            +15% this month
          </p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
            Recent Bot Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.success }}></div>
              <div className="flex-1">
                <p className="text-sm" style={{ color: colors.text }}>FAQ Bot answered 15 questions</p>
                <p className="text-xs" style={{ color: colors.muted }}>2 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
              <div className="flex-1">
                <p className="text-sm" style={{ color: colors.text }}>AI Assistant processed 8 requests</p>
                <p className="text-xs" style={{ color: colors.muted }}>5 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.warning }}></div>
              <div className="flex-1">
                <p className="text-sm" style={{ color: colors.text }}>Workflow automation triggered</p>
                <p className="text-xs" style={{ color: colors.muted }}>10 minutes ago</p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
            System Health
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: colors.text }}>Bot Response Rate</span>
                <span style={{ color: colors.text }}>94%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full"
                  style={{ backgroundColor: colors.success, width: '94%' }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: colors.text }}>Integration Uptime</span>
                <span style={{ color: colors.text }}>99.2%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full"
                  style={{ backgroundColor: colors.success, width: '99.2%' }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: colors.text }}>Archive Processing</span>
                <span style={{ color: colors.text }}>87%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full"
                  style={{ backgroundColor: colors.warning, width: '87%' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 rounded-lg" style={{ backgroundColor: colors.background, border: `1px solid ${colors.border}` }}>
        <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
          Quick Actions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveSection('bots')}
            className="p-4 rounded-lg border text-center hover:bg-opacity-50 transition-colors"
            style={{ borderColor: colors.border }}
          >
            <div className="text-2xl mb-2">🤖</div>
            <div className="text-sm font-medium" style={{ color: colors.text }}>
              Create Bot
            </div>
          </button>
          <button
            onClick={() => setActiveSection('archive')}
            className="p-4 rounded-lg border text-center hover:bg-opacity-50 transition-colors"
            style={{ borderColor: colors.border }}
          >
            <div className="text-2xl mb-2">📤</div>
            <div className="text-sm font-medium" style={{ color: colors.text }}>
              Export Data
            </div>
          </button>
          <button
            onClick={() => setActiveSection('integrations')}
            className="p-4 rounded-lg border text-center hover:bg-opacity-50 transition-colors"
            style={{ borderColor: colors.border }}
          >
            <div className="text-2xl mb-2">🔗</div>
            <div className="text-sm font-medium" style={{ color: colors.text }}>
              Add Integration
            </div>
          </button>
          <button
            className="p-4 rounded-lg border text-center hover:bg-opacity-50 transition-colors"
            style={{ borderColor: colors.border }}
          >
            <div className="text-2xl mb-2">⚙️</div>
            <div className="text-sm font-medium" style={{ color: colors.text }}>
              System Settings
            </div>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`h-full flex ${className}`} data-testid={testId}>
      {/* Sidebar */}
      <div className="w-64 border-r" style={{ borderColor: colors.border, backgroundColor: colors.surface }}>
        <div className="p-4">
          <h2 className="text-lg font-semibold" style={{ color: colors.text }}>
            Administration
          </h2>
          <p className="text-sm" style={{ color: colors.muted }}>
            System management and configuration
          </p>
        </div>
        <nav className="mt-4">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id as any)}
              className={`w-full text-left px-4 py-3 hover:bg-opacity-50 transition-colors ${
                activeSection === section.id ? 'border-r-2' : ''
              }`}
              style={{
                backgroundColor: activeSection === section.id ? colors.primary + '10' : 'transparent',
                borderColor: activeSection === section.id ? colors.primary : 'transparent',
              }}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{section.icon}</span>
                <div>
                  <div
                    className="font-medium"
                    style={{ color: activeSection === section.id ? colors.primary : colors.text }}
                  >
                    {section.label}
                  </div>
                  <div className="text-xs" style={{ color: colors.muted }}>
                    {section.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto" style={{ backgroundColor: colors.surface }}>
        {activeSection === 'overview' && renderOverview()}
        {activeSection === 'bots' && <BotManagement />}
        {activeSection === 'archive' && <ArchiveManagement />}
        {activeSection === 'integrations' && <IntegrationManagement />}
      </div>
    </div>
  );
};
