# AppDynamicContent Component

A unified component that combines the DynamicAppBottomBar with main content area to provide consistent UI patterns across the application.

## Overview

The `AppDynamicContent` component is designed to work alongside `DynamicAppHeader` to form a complete application layout. While `DynamicAppHeader` handles the top navigation, `AppDynamicContent` manages the bottom bar controls and main content area.

## Architecture

```
┌─────────────────────────────────────┐
│         DynamicAppHeader            │  ← Top navigation only
├─────────────────────────────────────┤
│                                     │
│         AppDynamicContent           │  ← Bottom bar + content
│  ┌─────────────────────────────────┐ │
│  │     DynamicAppBottomBar         │ │  ← Contextual controls
│  ├─────────────────────────────────┤ │
│  │                                 │ │
│  │        Main Content             │ │  ← Your content here
│  │                                 │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## Features

- **Unified Layout**: Combines bottom bar and content in a single component
- **Consistent Spacing**: Maintains proper spacing and responsive behavior
- **Flexible Content**: Accepts any content through children prop
- **Theme Integration**: Automatically applies theme colors and styles
- **Responsive Design**: Adapts to different screen sizes

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `view` | `DynamicAppBottomBarProps['view']` | Yes | Configuration for the bottom bar |
| `children` | `React.ReactNode` | Yes | Main content to display |
| `className` | `string` | No | Additional CSS classes for the container |
| `contentClassName` | `string` | No | Additional CSS classes for the content area |
| `data-testid` | `string` | No | Test identifier |

## Usage

### Basic Usage

```tsx
import { AppDynamicContent } from '../components/layout';

function MyAppPage() {
  const viewConfig = {
    title: 'Dashboard',
    actions: [
      { label: 'New', onClick: handleNew, isPrimary: true },
      { label: 'Export', onClick: handleExport },
    ],
    search: {
      onSearch: handleSearch,
      // ... other search config
    },
    pagination: {
      currentRange: '1-20 of 100',
      onNext: handleNext,
      onPrev: handlePrev,
    },
    viewModes: [
      { name: 'List', icon: <ListIcon /> },
      { name: 'Grid', icon: <GridIcon /> },
    ],
    activeViewMode: 'List',
  };

  return (
    <AppDynamicContent view={viewConfig}>
      <div className="space-y-6">
        <h2>My Dashboard Content</h2>
        {/* Your content here */}
      </div>
    </AppDynamicContent>
  );
}
```

### With DynamicAppHeader

```tsx
import { DynamicAppHeader, AppDynamicContent } from '../components/layout';

function CompleteAppLayout() {
  const headerConfig = {
    app: { /* app config */ },
    user: { /* user config */ },
  };

  const contentConfig = {
    view: { /* view config */ },
  };

  return (
    <div className="min-h-screen">
      <DynamicAppHeader {...headerConfig} />
      <AppDynamicContent {...contentConfig}>
        <YourMainContent />
      </AppDynamicContent>
    </div>
  );
}
```

### Custom Styling

```tsx
<AppDynamicContent
  view={viewConfig}
  className="custom-container-class"
  contentClassName="custom-content-class"
>
  <YourContent />
</AppDynamicContent>
```

## Content Area Styling

The content area includes:
- Maximum width constraint (`max-w-7xl`)
- Responsive horizontal padding (`px-4 sm:px-6 lg:px-8`)
- Vertical padding (`py-8`)
- Centered layout (`mx-auto`)

You can override these with the `contentClassName` prop if needed.

## Integration with DynamicAppBottomBar

This component automatically includes and configures the `DynamicAppBottomBar` with the provided view configuration. All bottom bar features are available:

- Action buttons
- Search functionality
- Pagination controls
- View mode switching
- Breadcrumb navigation

## Responsive Behavior

- **Mobile**: Optimized for touch interactions and smaller screens
- **Tablet**: Balanced layout with appropriate spacing
- **Desktop**: Full feature set with optimal spacing

## Accessibility

- Proper semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility

## Testing

The component includes comprehensive test coverage:

```bash
npm test AppDynamicContent
```

## Migration from Combined Components

If you were previously using DynamicAppHeader with embedded bottom bar:

### Before
```tsx
<DynamicAppHeader app={app} user={user} view={view} />
<main>
  <YourContent />
</main>
```

### After
```tsx
<DynamicAppHeader app={app} user={user} />
<AppDynamicContent view={view}>
  <YourContent />
</AppDynamicContent>
```

## Benefits of Separation

1. **Better Maintainability**: Clear separation of concerns
2. **Improved Reusability**: Components can be used independently
3. **Enhanced Flexibility**: Easier to customize layouts
4. **Cleaner Architecture**: More modular and testable code
