import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../../components/layout/DynamicAppHeader';
import { getAppById } from '../../data/mockApps';
import { BellIcon, UserIcon } from '../../components/icons';
import { DiscussLayout } from './components/layout/DiscussLayout';
import { ChannelsView } from './components/views/ChannelsView';
import { DirectMessagesView } from './components/views/DirectMessagesView';
import { TeamsView } from './components/views/TeamsView';
import { SettingsView } from './components/views/SettingsView';
import { AdminView } from './components/views/AdminView';

// Mock user data
const mockUserData = {
  name: '<PERSON>',
  avatar: (
    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
      <UserIcon className="w-5 h-5" />
    </div>
  ),
  notifications: [{ count: 3, icon: <BellIcon className="w-5 h-5" /> }],
};

export interface DiscussModuleProps {
  className?: string;
  'data-testid'?: string;
}

const DiscussModule: React.FC<DiscussModuleProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors } = useThemeStore();

  const menuId = searchParams.get('menu');
  const viewId = searchParams.get('view') || 'channels';

  // Get discuss app data
  const appData = getAppById('10'); // Discuss app ID

  // Redirect if not discuss module
  useEffect(() => {
    if (menuId !== '10') {
      navigate('/dashboard');
    }
  }, [menuId, navigate]);

  if (!appData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2" style={{ color: colors.text }}>
            Discuss module not found
          </h2>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Redirecting to dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Update nav links to show active state based on current view
  const updatedNavLinks = appData.navLinks
    ? appData.navLinks.map(link => ({
        ...link,
        isActive: link.href.includes(`view=${viewId}`),
      }))
    : [];

  const currentView = appData.views
    ? appData.views[viewId as keyof typeof appData.views] ||
      appData.views.channels
    : {
        title: 'Team Channels',
        content: 'Communicate with your team through organized channels.',
      };

  // Create view data for the header
  const viewData = {
    title: currentView.title,
    actions: [
      {
        label: 'New Channel',
        onClick: () => console.log('New Channel clicked'),
        isPrimary: true,
      },
      {
        label: 'Invite Users',
        onClick: () => console.log('Invite Users clicked'),
        isPrimary: false,
      },
    ],
    search: {
      filterTags: [],
      filterItems: [
        { id: 'unread', label: 'Unread', selected: false },
        { id: 'mentions', label: 'Mentions', selected: false },
        { id: 'starred', label: 'Starred', selected: false },
      ],
      groupByItems: [
        { id: 'channel', label: 'Channel' },
        { id: 'user', label: 'User' },
        { id: 'date', label: 'Date' },
      ],
      favoriteItems: [
        { id: 'recent', label: 'Recent Conversations' },
        { id: 'important', label: 'Important Messages' },
      ],
      onSearch: (query: string) => console.log('Search:', query),
      onTagRemove: (tagId: string) => console.log('Remove tag:', tagId),
      onFilterSelect: (filterId: string) => console.log('Filter select:', filterId),
      onGroupBySelect: (groupId: string) => console.log('Group by:', groupId),
      onFavoriteSelect: (favoriteId: string) => console.log('Favorite select:', favoriteId),
      onFavoriteDelete: (favoriteId: string) => console.log('Favorite delete:', favoriteId),
      onAddCustomFilter: () => console.log('Add custom filter'),
      onAddCustomGroup: () => console.log('Add custom group'),
      onSaveCurrentSearch: () => console.log('Save current search'),
    },
    pagination: {
      currentRange: '1-20 / 100',
      onNext: () => console.log('Next page'),
      onPrev: () => console.log('Previous page'),
    },
    viewModes: [
      { name: 'List', icon: '📋' },
      { name: 'Compact', icon: '⊞' },
      { name: 'Activity', icon: '📊' },
    ],
    activeViewMode: 'List',
  };

  const appHeaderData = {
    name: appData.title,
    icon: (
      <div
        className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg"
        style={{ backgroundColor: appData.color }}
      >
        {appData.icon}
      </div>
    ),
    navLinks: updatedNavLinks,
  };

  // Render the appropriate view based on viewId
  const renderView = () => {
    switch (viewId) {
      case 'channels':
        return <ChannelsView />;
      case 'messages':
        return <DirectMessagesView />;
      case 'teams':
        return <TeamsView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <ChannelsView />;
    }
  };

  return (
    <div
      className={`flex flex-col ${className}`}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Header */}
      <DynamicAppHeader
        app={appHeaderData}
        user={mockUserData}
        view={viewData}
      />

      {/* Main Discuss Layout */}
      <DiscussLayout>
        {renderView()}
      </DiscussLayout>
    </div>
  );
};

export default DiscussModule;
