// FAQ service for managing knowledge base and FAQ functionality
import type { 
  FAQEntry, 
  FAQCategory, 
  KnowledgeBase, 
  SmartResponse, 
  ResponseSuggestion,
  ApiResponse, 
  PaginatedResponse 
} from '../types';

const API_BASE = '/api/discuss/faq';

export interface CreateFAQEntryRequest {
  question: string;
  answer: string;
  keywords: string[];
  category: string;
  tags: string[];
  metadata?: Record<string, any>;
}

export interface UpdateFAQEntryRequest {
  question?: string;
  answer?: string;
  keywords?: string[];
  category?: string;
  tags?: string[];
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface FAQSearchRequest {
  query: string;
  category?: string;
  tags?: string[];
  confidenceThreshold?: number;
  maxResults?: number;
  includeInactive?: boolean;
}

export interface FAQSearchResult {
  entry: FAQEntry;
  confidence: number;
  matchedKeywords: string[];
  snippet: string;
}

export const faqService = {
  // Knowledge Base Management
  async getKnowledgeBase(): Promise<ApiResponse<KnowledgeBase>> {
    const response = await fetch(`${API_BASE}/knowledge-base`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch knowledge base');
    }
    
    return response.json();
  },

  async updateKnowledgeBaseSettings(settings: Partial<KnowledgeBase['settings']>): Promise<ApiResponse<KnowledgeBase>> {
    const response = await fetch(`${API_BASE}/knowledge-base/settings`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update knowledge base settings');
    }
    
    return response.json();
  },

  // FAQ Entry Management
  async getFAQEntries(
    page: number = 1, 
    pageSize: number = 50,
    category?: string,
    isActive?: boolean
  ): Promise<PaginatedResponse<FAQEntry>> {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
    });
    
    if (category) params.append('category', category);
    if (isActive !== undefined) params.append('isActive', isActive.toString());

    const response = await fetch(`${API_BASE}/entries?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch FAQ entries');
    }
    
    return response.json();
  },

  async getFAQEntry(id: string): Promise<ApiResponse<FAQEntry>> {
    const response = await fetch(`${API_BASE}/entries/${id}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch FAQ entry');
    }
    
    return response.json();
  },

  async createFAQEntry(request: CreateFAQEntryRequest): Promise<ApiResponse<FAQEntry>> {
    const response = await fetch(`${API_BASE}/entries`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create FAQ entry');
    }
    
    return response.json();
  },

  async updateFAQEntry(id: string, request: UpdateFAQEntryRequest): Promise<ApiResponse<FAQEntry>> {
    const response = await fetch(`${API_BASE}/entries/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update FAQ entry');
    }
    
    return response.json();
  },

  async deleteFAQEntry(id: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/entries/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete FAQ entry');
    }
    
    return response.json();
  },

  // FAQ Search and Matching
  async searchFAQ(request: FAQSearchRequest): Promise<ApiResponse<FAQSearchResult[]>> {
    const response = await fetch(`${API_BASE}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to search FAQ');
    }
    
    return response.json();
  },

  async findBestMatch(query: string, context?: Record<string, any>): Promise<ApiResponse<FAQSearchResult | null>> {
    const response = await fetch(`${API_BASE}/match`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, context }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to find FAQ match');
    }
    
    return response.json();
  },

  // Smart Response Generation
  async generateSmartResponse(
    query: string, 
    context: Record<string, any>
  ): Promise<ApiResponse<SmartResponse>> {
    const response = await fetch(`${API_BASE}/smart-response`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, context }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate smart response');
    }
    
    return response.json();
  },

  async getResponseSuggestions(
    messageId: string,
    context?: Record<string, any>
  ): Promise<ApiResponse<ResponseSuggestion[]>> {
    const response = await fetch(`${API_BASE}/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messageId, context }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to get response suggestions');
    }
    
    return response.json();
  },

  // Category Management
  async getCategories(): Promise<ApiResponse<FAQCategory[]>> {
    const response = await fetch(`${API_BASE}/categories`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch FAQ categories');
    }
    
    return response.json();
  },

  async createCategory(category: Omit<FAQCategory, 'id' | 'entryCount'>): Promise<ApiResponse<FAQCategory>> {
    const response = await fetch(`${API_BASE}/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(category),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create FAQ category');
    }
    
    return response.json();
  },

  async updateCategory(id: string, updates: Partial<FAQCategory>): Promise<ApiResponse<FAQCategory>> {
    const response = await fetch(`${API_BASE}/categories/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update FAQ category');
    }
    
    return response.json();
  },

  async deleteCategory(id: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/categories/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete FAQ category');
    }
    
    return response.json();
  },

  // Analytics and Feedback
  async recordFAQUsage(entryId: string, helpful: boolean, feedback?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/entries/${entryId}/usage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ helpful, feedback }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to record FAQ usage');
    }
    
    return response.json();
  },

  async getFAQAnalytics(period: 'day' | 'week' | 'month' = 'week'): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/analytics?period=${period}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch FAQ analytics');
    }
    
    return response.json();
  },
};
