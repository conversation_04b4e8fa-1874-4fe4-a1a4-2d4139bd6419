import { useThemeStore } from '../../stores/themeStore';

export interface DevUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'moderator' | 'guest';
  avatar?: string;
  permissions?: string[];
}

export interface DevLoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin: (user: DevUser) => void;
}

const devUsers: DevUser[] = [
  {
    id: 'dev-admin',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '👨‍💼',
    permissions: ['read', 'write', 'delete', 'admin'],
  },
  {
    id: 'dev-user',
    name: 'Regular User',
    email: '<EMAIL>',
    role: 'user',
    avatar: '👤',
    permissions: ['read', 'write'],
  },
  {
    id: 'dev-moderator',
    name: 'Moderator User',
    email: '<EMAIL>',
    role: 'moderator',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate'],
  },
  {
    id: 'dev-guest',
    name: 'Guest User',
    email: '<EMAIL>',
    role: 'guest',
    avatar: '👻',
    permissions: ['read'],
  },
];

export function DevLoginModal({
  isOpen,
  onClose,
  onLogin,
}: DevLoginModalProps) {
  const { colors } = useThemeStore();

  if (!isOpen) return null;

  const handleUserSelect = (user: DevUser) => {
    onLogin(user);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderator':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'user':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'guest':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className="relative w-full max-w-md mx-4 rounded-xl shadow-2xl border"
        style={{
          backgroundColor: colors.background,
          borderColor: colors.border,
        }}
      >
        {/* Header */}
        <div
          className="flex items-center justify-between p-6 border-b"
          style={{ borderColor: colors.border }}
        >
          <div>
            <h2
              className="text-xl font-semibold"
              style={{ color: colors.text }}
            >
              🔧 Development Login
            </h2>
            <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
              Choose a user type for testing
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            aria-label="Close modal"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* User List */}
        <div className="p-6 space-y-3">
          {devUsers.map(user => (
            <button
              key={user.id}
              onClick={() => handleUserSelect(user)}
              className="w-full p-4 rounded-lg border-2 border-transparent hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-left group"
              style={{ backgroundColor: `${colors.background}` }}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">{user.avatar}</div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3
                      className="font-medium truncate"
                      style={{ color: colors.text }}
                    >
                      {user.name}
                    </h3>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(user.role)}`}
                    >
                      {user.role}
                    </span>
                  </div>
                  <p
                    className="text-sm truncate mt-1"
                    style={{ color: colors.textSecondary }}
                  >
                    {user.email}
                  </p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {user.permissions?.slice(0, 3).map(permission => (
                      <span
                        key={permission}
                        className="px-2 py-0.5 text-xs rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                      >
                        {permission}
                      </span>
                    ))}
                    {user.permissions && user.permissions.length > 3 && (
                      <span className="px-2 py-0.5 text-xs rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                        +{user.permissions.length - 3}
                      </span>
                    )}
                  </div>
                </div>
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <svg
                    className="w-5 h-5 text-blue-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Footer */}
        <div
          className="px-6 py-4 border-t bg-gray-50 dark:bg-gray-800/50 rounded-b-xl"
          style={{ borderColor: colors.border }}
        >
          <p
            className="text-xs text-center"
            style={{ color: colors.textSecondary }}
          >
            ⚠️ Development mode only - These users are for testing purposes
          </p>
        </div>
      </div>
    </div>
  );
}
