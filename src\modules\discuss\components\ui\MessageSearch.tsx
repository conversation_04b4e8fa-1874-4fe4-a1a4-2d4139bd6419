import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Message, User, Channel } from '../../types';

export interface SearchResult {
  message: Message;
  author: User;
  channel: Channel;
  snippet: string;
  highlights: number[];
}

export interface MessageSearchProps {
  onSearch: (query: string, filters: SearchFilters) => Promise<SearchResult[]>;
  onResultClick: (result: SearchResult) => void;
  placeholder?: string;
  className?: string;
  'data-testid'?: string;
}

export interface SearchFilters {
  channels?: string[];
  authors?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  hasAttachments?: boolean;
  messageType?: 'all' | 'messages' | 'files' | 'links';
}

export const MessageSearch: React.FC<MessageSearchProps> = ({
  onSearch,
  onResultClick,
  placeholder = 'Search messages...',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [isOpen, setIsOpen] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced search
  const performSearch = useCallback(async (searchQuery: string, searchFilters: SearchFilters) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const searchResults = await onSearch(searchQuery, searchFilters);
      setResults(searchResults);
    } catch (error) {
      console.error('Search failed:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [onSearch]);

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      performSearch(query, filters);
    }, 300);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, filters, performSearch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setIsOpen(true);
  };

  const handleResultClick = (result: SearchResult) => {
    onResultClick(result);
    setIsOpen(false);
    setQuery('');
    setResults([]);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const diffInDays = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays < 7) {
      return messageDate.toLocaleDateString([], { weekday: 'short' });
    } else {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const highlightText = (text: string, highlights: number[]) => {
    if (highlights.length === 0) return text;

    const parts = [];
    let lastIndex = 0;

    highlights.forEach((index) => {
      if (index > lastIndex) {
        parts.push(text.slice(lastIndex, index));
      }
      const queryLength = query.length;
      parts.push(
        <mark
          key={index}
          className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded"
        >
          {text.slice(index, index + queryLength)}
        </mark>
      );
      lastIndex = index + queryLength;
    });

    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  };

  return (
    <div ref={searchRef} className={`relative ${className}`} data-testid={testId}>
      {/* Search Input */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full pl-10 pr-12 py-2 border rounded-lg bg-transparent outline-none transition-colors"
          style={{
            borderColor: isOpen ? colors.primary : colors.border,
            color: colors.text,
          }}
        />
        
        {/* Search Icon */}
        <div
          className="absolute left-3 top-1/2 transform -translate-y-1/2"
          style={{ color: colors.textSecondary }}
        >
          🔍
        </div>

        {/* Filter Button */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
            showFilters ? 'bg-gray-100 dark:bg-gray-700' : ''
          }`}
          style={{ color: colors.textSecondary }}
        >
          ⚙️
        </button>

        {/* Loading Indicator */}
        {isLoading && (
          <div
            className="absolute right-10 top-1/2 transform -translate-y-1/2"
            style={{ color: colors.textSecondary }}
          >
            <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full" />
          </div>
        )}
      </div>

      {/* Search Results */}
      {isOpen && (query || results.length > 0) && (
        <div
          className="absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg max-h-96 overflow-y-auto z-50"
          style={{
            backgroundColor: colors.background,
            borderColor: colors.border,
          }}
        >
          {results.length > 0 ? (
            <div className="py-2">
              {results.map((result, index) => (
                <button
                  key={`${result.message.id}-${index}`}
                  onClick={() => handleResultClick(result)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start space-x-3">
                    {/* Author Avatar */}
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0"
                      style={{ backgroundColor: colors.primary }}
                    >
                      {result.author.avatar || result.author.name.charAt(0).toUpperCase()}
                    </div>

                    {/* Message Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm" style={{ color: colors.text }}>
                          {result.author.name}
                        </span>
                        <span className="text-xs" style={{ color: colors.textSecondary }}>
                          in #{result.channel.name}
                        </span>
                        <span className="text-xs" style={{ color: colors.textSecondary }}>
                          {formatTimestamp(result.message.timestamp)}
                        </span>
                      </div>
                      <div className="text-sm" style={{ color: colors.text }}>
                        {highlightText(result.snippet, result.highlights)}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : query && !isLoading ? (
            <div className="px-4 py-8 text-center">
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                No messages found for "{query}"
              </p>
            </div>
          ) : null}
        </div>
      )}

      {/* Search Filters */}
      {showFilters && (
        <div
          className="absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg p-4 z-40"
          style={{
            backgroundColor: colors.background,
            borderColor: colors.border,
          }}
        >
          <div className="space-y-4">
            <h3 className="font-medium text-sm" style={{ color: colors.text }}>
              Search Filters
            </h3>
            
            {/* Date Range */}
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs mb-1" style={{ color: colors.textSecondary }}>
                  From
                </label>
                <input
                  type="date"
                  value={filters.dateFrom?.toISOString().split('T')[0] || ''}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateFrom: e.target.value ? new Date(e.target.value) : undefined
                  }))}
                  className="w-full px-2 py-1 text-xs border rounded bg-transparent"
                  style={{
                    borderColor: colors.border,
                    color: colors.text,
                  }}
                />
              </div>
              <div>
                <label className="block text-xs mb-1" style={{ color: colors.textSecondary }}>
                  To
                </label>
                <input
                  type="date"
                  value={filters.dateTo?.toISOString().split('T')[0] || ''}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateTo: e.target.value ? new Date(e.target.value) : undefined
                  }))}
                  className="w-full px-2 py-1 text-xs border rounded bg-transparent"
                  style={{
                    borderColor: colors.border,
                    color: colors.text,
                  }}
                />
              </div>
            </div>

            {/* Message Type */}
            <div>
              <label className="block text-xs mb-1" style={{ color: colors.textSecondary }}>
                Message Type
              </label>
              <select
                value={filters.messageType || 'all'}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  messageType: e.target.value as SearchFilters['messageType']
                }))}
                className="w-full px-2 py-1 text-xs border rounded bg-transparent"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              >
                <option value="all">All Messages</option>
                <option value="messages">Text Only</option>
                <option value="files">With Files</option>
                <option value="links">With Links</option>
              </select>
            </div>

            {/* Clear Filters */}
            <button
              onClick={() => setFilters({})}
              className="text-xs px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              style={{ color: colors.primary }}
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
