/**
 * Enhanced ErrorBoundary Storybook Stories
 * Comprehensive stories showcasing enhanced error boundary functionality
 */

import type { Meta, StoryObj } from '@storybook/react-vite';
import React, { useState } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { ErrorBoundaryProvider, useErrorBoundaryContext, withErrorBoundary } from './ErrorBoundaryProvider';
import { useErrorBoundary } from './useErrorBoundary';
import Button from '../../ui/Button/Button';

const meta: Meta<typeof ErrorBoundary> = {
  title: 'Common/ErrorBoundary',
  component: ErrorBoundary,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'React error boundary with enhanced error handling, reporting, and recovery features.',
      },
    },
  },
  argTypes: {
    children: {
      description: 'Child components to wrap with error boundary',
      control: false,
    },
    onError: {
      description: 'Custom error handler function',
      action: 'onError',
    },
    enableAutoRecovery: {
      description: 'Enable automatic error recovery',
      control: 'boolean',
    },
    enableReporting: {
      description: 'Enable error reporting',
      control: 'boolean',
    },
    isolateErrors: {
      description: 'Isolate errors to prevent propagation',
      control: 'boolean',
    },
    resetOnPropsChange: {
      description: 'Reset error when props change',
      control: 'boolean',
    },
    level: {
      description: 'Error boundary level',
      control: 'select',
      options: ['page', 'section', 'component'],
    },
    componentName: {
      description: 'Name of the component being wrapped',
      control: 'text',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ErrorBoundary>;

// Component that throws an error
const ErrorThrowingComponent: React.FC<{
  shouldThrow?: boolean;
  errorType?: string;
}> = ({ shouldThrow = false, errorType = 'runtime' }) => {
  if (shouldThrow) {
    switch (errorType) {
      case 'runtime':
        throw new Error('This is a runtime error for testing purposes');
      case 'network': {
        const networkError = new Error('Network request failed');
        networkError.name = 'NetworkError';
        throw networkError;
      }
      case 'chunk': {
        const chunkError = new Error('Loading chunk 2 failed');
        throw chunkError;
      }
      case 'auth': {
        const authError = new Error('Authentication failed - 401 Unauthorized');
        throw authError;
      }
      default:
        throw new Error('Unknown error type');
    }
  }

  return (
    <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
      <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
        Component Working Correctly
      </h3>
      <p className="text-green-600 dark:text-green-300">
        This component is rendering without any errors. Click the button below
        to trigger an error.
      </p>
    </div>
  );
};

// Interactive component for testing
const InteractiveErrorComponent: React.FC<{ errorType?: string }> = ({
  errorType = 'runtime',
}) => {
  const [shouldThrow, setShouldThrow] = useState(false);

  return (
    <div className="space-y-4">
      <ErrorThrowingComponent shouldThrow={shouldThrow} errorType={errorType} />
      <Button
        onClick={() => setShouldThrow(true)}
        variant="danger"
        className="w-full"
      >
        Trigger {errorType} Error
      </Button>
    </div>
  );
};

// Default story
export const Default: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    isolateErrors: false,
    resetOnPropsChange: false,
    level: 'component',
    componentName: 'TestComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Runtime error
export const RuntimeError: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'component',
    componentName: 'RuntimeTestComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent errorType="runtime" />
    </ErrorBoundary>
  ),
};

// Network error
export const NetworkError: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'section',
    componentName: 'NetworkTestComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent errorType="network" />
    </ErrorBoundary>
  ),
};

// Chunk load error
export const ChunkLoadError: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'component',
    componentName: 'ChunkTestComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent errorType="chunk" />
    </ErrorBoundary>
  ),
};

// Authentication error
export const AuthenticationError: Story = {
  args: {
    enableAutoRecovery: false,
    enableReporting: true,
    level: 'page',
    componentName: 'AuthTestComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent errorType="auth" />
    </ErrorBoundary>
  ),
};

// Page level error boundary
export const PageLevel: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'page',
    componentName: 'PageComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <div className="min-h-96 p-8">
        <h1 className="text-2xl font-bold mb-4">Page Content</h1>
        <InteractiveErrorComponent />
      </div>
    </ErrorBoundary>
  ),
};

// Section level error boundary
export const SectionLevel: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'section',
    componentName: 'SectionComponent',
  },
  render: args => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Page with Multiple Sections</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Working Section</h2>
          <p>This section works fine.</p>
        </div>

        <ErrorBoundary {...args}>
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Error-Prone Section</h2>
            <InteractiveErrorComponent />
          </div>
        </ErrorBoundary>
      </div>
    </div>
  ),
};

// Component level error boundary
export const ComponentLevel: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'component',
    componentName: 'SmallComponent',
  },
  render: args => (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Page with Multiple Components</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <h3 className="font-semibold mb-2">Component 1</h3>
          <p>Working fine</p>
        </div>

        <ErrorBoundary {...args}>
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h3 className="font-semibold mb-2">Component 2</h3>
            <InteractiveErrorComponent />
          </div>
        </ErrorBoundary>

        <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <h3 className="font-semibold mb-2">Component 3</h3>
          <p>Also working fine</p>
        </div>
      </div>
    </div>
  ),
};

// Without auto recovery
export const WithoutAutoRecovery: Story = {
  args: {
    enableAutoRecovery: false,
    enableReporting: true,
    level: 'component',
    componentName: 'NoAutoRecoveryComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Without reporting
export const WithoutReporting: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: false,
    level: 'component',
    componentName: 'NoReportingComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// With error isolation
export const WithErrorIsolation: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    isolateErrors: true,
    level: 'component',
    componentName: 'IsolatedComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Nested error boundaries
export const NestedErrorBoundaries: Story = {
  render: () => (
    <ErrorBoundary level="page" componentName="OuterBoundary">
      <div className="space-y-6 p-6">
        <h1 className="text-2xl font-bold">Nested Error Boundaries</h1>

        <ErrorBoundary level="section" componentName="SectionBoundary">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">
              Section with Component Boundaries
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ErrorBoundary level="component" componentName="Component1">
                <InteractiveErrorComponent />
              </ErrorBoundary>

              <ErrorBoundary level="component" componentName="Component2">
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <h3 className="font-semibold">Working Component</h3>
                  <p>This component is working fine.</p>
                </div>
              </ErrorBoundary>
            </div>
          </div>
        </ErrorBoundary>
      </div>
    </ErrorBoundary>
  ),
};

// Enhanced features stories

// Performance monitoring story
export const WithPerformanceMonitoring: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    enablePerformanceMonitoring: true,
    level: 'component',
    componentName: 'PerformanceMonitoredComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Accessibility features story
export const WithAccessibilityFeatures: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    enableAccessibilityFeatures: true,
    level: 'component',
    componentName: 'AccessibleComponent',
    'aria-label': 'Error boundary for accessible component',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Custom recovery strategies story
export const WithCustomRecoveryStrategies: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    level: 'component',
    componentName: 'CustomRecoveryComponent',
    customRecoveryStrategies: [
      {
        name: 'Custom Retry',
        condition: (error) => error.type === 'RUNTIME',
        handler: async (error) => {
          console.log('Executing custom recovery for:', error.message);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return true;
        },
      },
    ],
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Graceful degradation story
export const WithGracefulDegradation: Story = {
  args: {
    enableAutoRecovery: true,
    enableReporting: true,
    gracefulDegradation: true,
    level: 'component',
    componentName: 'GracefulDegradationComponent',
  },
  render: args => (
    <ErrorBoundary {...args}>
      <InteractiveErrorComponent />
    </ErrorBoundary>
  ),
};

// Error boundary provider story
const ProviderExample: React.FC = () => {
  const { reportError, errorHistory, globalErrorCount } = useErrorBoundaryContext();
  
  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-semibold">Error Boundary Provider Example</h3>
      <p>Global error count: {globalErrorCount}</p>
      <p>Error history length: {errorHistory.length}</p>
      
      <div className="space-x-2">
        <Button
          onClick={() => reportError(new Error('Manual error report'))}
          variant="secondary"
        >
          Report Error Manually
        </Button>
        
        <Button
          onClick={() => {
            throw new Error('Thrown error for testing');
          }}
          variant="danger"
        >
          Throw Error
        </Button>
      </div>
      
      <InteractiveErrorComponent />
    </div>
  );
};

export const WithErrorBoundaryProvider: Story = {
  render: () => (
    <ErrorBoundaryProvider
      enableGlobalErrorHandling={true}
      enableUnhandledRejectionCapture={true}
      maxGlobalErrors={5}
    >
      <ProviderExample />
    </ErrorBoundaryProvider>
  ),
};

// Hook usage example
const HookExample: React.FC = () => {
  const { captureError, showBoundary } = useErrorBoundary({
    componentName: 'HookExampleComponent',
    enableAutoReport: true,
  });
  
  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-semibold">useErrorBoundary Hook Example</h3>
      
      <div className="space-x-2">
        <Button
          onClick={() => captureError('Error captured via hook')}
          variant="secondary"
        >
          Capture Error
        </Button>
        
        <Button
          onClick={() => showBoundary('Error thrown to boundary')}
          variant="danger"
        >
          Show Boundary
        </Button>
      </div>
    </div>
  );
};

export const WithErrorBoundaryHook: Story = {
  render: () => (
    <ErrorBoundary
      enableAutoRecovery={true}
      enableReporting={true}
      componentName="HookDemoComponent"
    >
      <HookExample />
    </ErrorBoundary>
  ),
};

// HOC example
const SimpleComponent: React.FC<{ shouldError?: boolean }> = ({ shouldError }) => {
  if (shouldError) {
    throw new Error('Error from HOC wrapped component');
  }
  
  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded">
      <h3 className="font-semibold text-green-800">HOC Wrapped Component</h3>
      <p className="text-green-600">This component is wrapped with withErrorBoundary HOC</p>
    </div>
  );
};

const WrappedComponent = withErrorBoundary(SimpleComponent, {
  enableAutoRecovery: true,
  enableReporting: true,
  level: 'component',
});

export const WithHOCWrapper: Story = {
  render: () => {
    const [shouldError, setShouldError] = useState(false);
    
    return (
      <div className="space-y-4">
        <Button
          onClick={() => setShouldError(!shouldError)}
          variant={shouldError ? "danger" : "secondary"}
        >
          {shouldError ? 'Fix Component' : 'Break Component'}
        </Button>
        
        <WrappedComponent shouldError={shouldError} />
      </div>
    );
  },
};
