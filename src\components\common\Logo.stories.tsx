import type { Meta, StoryObj } from '@storybook/react-vite';
import { Logo } from './Logo';

const meta: Meta<typeof Logo> = {
  title: 'Common/Logo',
  component: Logo,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A reusable logo component with multiple variants and sizes for consistent branding across the application.',
      },
    },
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the logo',
    },
    variant: {
      control: 'select',
      options: ['full', 'icon', 'text'],
      description: 'Logo variant to display',
    },
    showText: {
      control: 'boolean',
      description:
        'Whether to show text alongside the icon (only applies to full variant)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Logo>;

export const Default: Story = {
  args: {
    size: 'md',
    variant: 'full',
    showText: true,
  },
};

export const IconOnly: Story = {
  args: {
    size: 'md',
    variant: 'icon',
  },
};

export const TextOnly: Story = {
  args: {
    size: 'md',
    variant: 'text',
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    variant: 'full',
    showText: true,
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    variant: 'full',
    showText: true,
  },
};

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
    variant: 'full',
    showText: true,
  },
};

export const WithoutText: Story = {
  args: {
    size: 'md',
    variant: 'full',
    showText: false,
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex flex-col gap-8 items-center">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">All Sizes</h3>
        <div className="flex items-center gap-6">
          <div className="text-center">
            <Logo size="sm" />
            <p className="text-sm mt-2 text-slate-600">Small</p>
          </div>
          <div className="text-center">
            <Logo size="md" />
            <p className="text-sm mt-2 text-slate-600">Medium</p>
          </div>
          <div className="text-center">
            <Logo size="lg" />
            <p className="text-sm mt-2 text-slate-600">Large</p>
          </div>
          <div className="text-center">
            <Logo size="xl" />
            <p className="text-sm mt-2 text-slate-600">Extra Large</p>
          </div>
        </div>
      </div>
    </div>
  ),
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-8 items-center">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">All Variants</h3>
        <div className="flex flex-col gap-6">
          <div className="text-center">
            <Logo variant="full" size="lg" />
            <p className="text-sm mt-2 text-slate-600">Full Logo</p>
          </div>
          <div className="text-center">
            <Logo variant="icon" size="lg" />
            <p className="text-sm mt-2 text-slate-600">Icon Only</p>
          </div>
          <div className="text-center">
            <Logo variant="text" size="lg" />
            <p className="text-sm mt-2 text-slate-600">Text Only</p>
          </div>
        </div>
      </div>
    </div>
  ),
};

export const DarkTheme: Story = {
  args: {
    size: 'lg',
    variant: 'full',
    showText: true,
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
  decorators: [
    Story => (
      <div className="dark">
        <Story />
      </div>
    ),
  ],
};
