import type { Meta, StoryObj } from '@storybook/react-vite';
import Card from './Card';

const meta: Meta<typeof Card> = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A flexible card component for displaying content with various styling options and interactive states.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'elevated', 'outlined', 'filled'],
      description: 'Visual style variant of the card',
    },
    padding: {
      control: { type: 'select' },
      options: ['none', 'sm', 'md', 'lg'],
      description: 'Internal padding of the card',
    },
    hoverable: {
      control: { type: 'boolean' },
      description: 'Whether the card has hover effects',
    },
    clickable: {
      control: { type: 'boolean' },
      description: 'Whether the card is clickable',
    },
    onClick: {
      action: 'clicked',
      description: 'Function called when card is clicked (if clickable)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const SampleContent = () => (
  <div>
    <h3 className="text-lg font-semibold mb-2">Card Title</h3>
    <p className="text-gray-600 mb-4">
      This is some sample content inside the card. It demonstrates how the card
      component can be used to display various types of information.
    </p>
    <div className="flex space-x-2">
      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">
        Tag 1
      </span>
      <span className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded">
        Tag 2
      </span>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    children: <SampleContent />,
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: <SampleContent />,
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: <SampleContent />,
  },
};

export const Filled: Story = {
  args: {
    variant: 'filled',
    children: <SampleContent />,
  },
};

export const NoPadding: Story = {
  args: {
    padding: 'none',
    children: (
      <div className="p-4">
        <SampleContent />
      </div>
    ),
  },
};

export const SmallPadding: Story = {
  args: {
    padding: 'sm',
    children: <SampleContent />,
  },
};

export const LargePadding: Story = {
  args: {
    padding: 'lg',
    children: <SampleContent />,
  },
};

export const Hoverable: Story = {
  args: {
    variant: 'elevated',
    hoverable: true,
    children: <SampleContent />,
  },
};

export const Clickable: Story = {
  args: {
    variant: 'elevated',
    clickable: true,
    children: (
      <div>
        <h3 className="text-lg font-semibold mb-2">Clickable Card</h3>
        <p className="text-gray-600">
          Click me to see the action in the Actions panel!
        </p>
      </div>
    ),
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 max-w-4xl">
      <Card variant="default">
        <h4 className="font-semibold mb-2">Default</h4>
        <p className="text-sm text-gray-600">Standard card with border</p>
      </Card>
      <Card variant="elevated">
        <h4 className="font-semibold mb-2">Elevated</h4>
        <p className="text-sm text-gray-600">Card with shadow elevation</p>
      </Card>
      <Card variant="outlined">
        <h4 className="font-semibold mb-2">Outlined</h4>
        <p className="text-sm text-gray-600">Card with prominent border</p>
      </Card>
      <Card variant="filled">
        <h4 className="font-semibold mb-2">Filled</h4>
        <p className="text-sm text-gray-600">Card with filled background</p>
      </Card>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};
