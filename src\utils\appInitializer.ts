import { useAppStore } from '../stores/appStore';
import { useThemeStore } from '../stores/themeStore';
import { initializeAppConfig } from './configLoader';

/**
 * Initializes the application with configuration and stores
 */
export async function initializeApp() {
  try {
    console.log('Initializing application...');

    // Load configuration from TypeScript config
    const config = initializeAppConfig();

    // Initialize app store with configuration
    const appStore = useAppStore.getState();
    appStore.setLoading(true);

    if (config) {
      appStore.loadFromConfig(config);
    }

    // Initialize theme store with enhanced system detection
    const themeStore = useThemeStore.getState();

    // Initialize system theme detection
    themeStore.initializeSystemTheme();

    // Set theme based on configuration, saved preference, or system preference
    const configTheme = (config as any)?.ui?.defaultTheme || 'system';
    const savedTheme = themeStore.theme || configTheme;
    themeStore.setTheme(savedTheme);

    // Apply CSS variables immediately
    themeStore.applyCSSVariables();

    // Mark app as initialized
    appStore.setInitialized(true);
    appStore.setLoading(false);

    console.log('Application initialized successfully');

    return {
      success: true,
      config,
    };
  } catch (error) {
    console.error('Failed to initialize application:', error);

    // Set error state but still mark as initialized to prevent infinite loading
    const appStore = useAppStore.getState();
    appStore.setInitialized(true);
    appStore.setLoading(false);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * React hook for app initialization
 */
export function useAppInitialization() {
  const { isInitialized, isLoading } = useAppStore();

  return {
    isInitialized,
    isLoading,
    initialize: initializeApp,
  };
}
