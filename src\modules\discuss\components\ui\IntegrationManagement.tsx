import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { integrationService, webhookService, apiKeyService } from '../../services';
import type { ExternalIntegration, WebhookEndpoint, APIKey } from '../../types';

export interface IntegrationManagementProps {
  className?: string;
  'data-testid'?: string;
}

export const IntegrationManagement: React.FC<IntegrationManagementProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [activeTab, setActiveTab] = useState<'integrations' | 'webhooks' | 'api-keys'>('integrations');
  const [integrations, setIntegrations] = useState<ExternalIntegration[]>([]);
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([]);
  const [apiKeys, setApiKeys] = useState<Omit<APIKey, 'key'>[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      switch (activeTab) {
        case 'integrations':
          const integrationsResponse = await integrationService.getIntegrations();
          setIntegrations(integrationsResponse.data);
          break;
        case 'webhooks':
          const webhooksResponse = await webhookService.getWebhooks();
          setWebhooks(webhooksResponse.data);
          break;
        case 'api-keys':
          const apiKeysResponse = await apiKeyService.getAPIKeys();
          setApiKeys(apiKeysResponse.data);
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestIntegration = async (integrationId: string) => {
    try {
      const response = await integrationService.testIntegration(integrationId);
      alert(`Test ${response.data.status}: ${response.data.message}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Test failed');
    }
  };

  const handleSyncIntegration = async (integrationId: string) => {
    try {
      await integrationService.syncIntegration(integrationId);
      alert('Sync started successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sync failed');
    }
  };

  const handleTestWebhook = async (webhookId: string) => {
    try {
      const response = await webhookService.testWebhook(webhookId);
      alert(`Webhook test completed: ${response.data.status}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Webhook test failed');
    }
  };

  const getIntegrationIcon = (type: ExternalIntegration['type']) => {
    switch (type) {
      case 'slack': return '💬';
      case 'teams': return '👥';
      case 'whatsapp': return '📱';
      case 'email': return '📧';
      case 'webhook': return '🔗';
      case 'api': return '🔌';
      case 'erp': return '🏢';
      default: return '🔧';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'enabled':
        return colors.success;
      case 'pending':
      case 'processing':
        return colors.warning;
      case 'error':
      case 'failed':
      case 'inactive':
        return colors.error;
      default:
        return colors.muted;
    }
  };

  const tabs = [
    { id: 'integrations', label: 'External Integrations', icon: '🔗' },
    { id: 'webhooks', label: 'Webhooks', icon: '🪝' },
    { id: 'api-keys', label: 'API Keys', icon: '🔑' },
  ];

  return (
    <div className={`p-6 ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: colors.text }}>
            Integration Management
          </h2>
          <p className="text-sm mt-1" style={{ color: colors.muted }}>
            Manage external integrations, webhooks, and API access
          </p>
        </div>
        <button
          className="px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity"
          style={{ backgroundColor: colors.primary }}
        >
          Add Integration
        </button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 border-b" style={{ borderColor: colors.border }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`px-4 py-2 font-medium text-sm rounded-t-lg transition-colors ${
              activeTab === tab.id
                ? 'border-b-2'
                : 'hover:bg-opacity-50'
            }`}
            style={{
              color: activeTab === tab.id ? colors.primary : colors.muted,
              borderColor: activeTab === tab.id ? colors.primary : 'transparent',
              backgroundColor: activeTab === tab.id ? colors.primary + '10' : 'transparent',
            }}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 rounded-lg" style={{ backgroundColor: colors.error + '20', color: colors.error }}>
          {error}
        </div>
      )}

      {/* Content */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      ) : (
        <div>
          {/* External Integrations Tab */}
          {activeTab === 'integrations' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {integrations.map((integration) => (
                <div
                  key={integration.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getIntegrationIcon(integration.type)}</span>
                      <div>
                        <h3 className="font-medium" style={{ color: colors.text }}>
                          {integration.name}
                        </h3>
                        <p className="text-sm" style={{ color: colors.muted }}>
                          {integration.type}
                        </p>
                      </div>
                    </div>
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: getStatusColor(integration.status) }}
                    />
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span style={{ color: colors.muted }}>Status:</span>
                      <span
                        className="px-2 py-1 rounded text-xs"
                        style={{
                          backgroundColor: getStatusColor(integration.status) + '20',
                          color: getStatusColor(integration.status),
                        }}
                      >
                        {integration.status}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span style={{ color: colors.muted }}>Sync Direction:</span>
                      <span style={{ color: colors.text }}>{integration.config.syncDirection}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span style={{ color: colors.muted }}>Last Sync:</span>
                      <span style={{ color: colors.text }}>
                        {integration.lastSync?.toLocaleDateString() || 'Never'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span style={{ color: colors.muted }}>Success Rate:</span>
                      <span style={{ color: colors.text }}>
                        {integration.syncStats.totalSynced > 0
                          ? Math.round((integration.syncStats.successfulSyncs / integration.syncStats.totalSynced) * 100)
                          : 0}%
                      </span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleTestIntegration(integration.id)}
                      className="flex-1 px-3 py-2 text-sm rounded border hover:bg-opacity-50 transition-colors"
                      style={{ borderColor: colors.border, color: colors.text }}
                    >
                      Test
                    </button>
                    <button
                      onClick={() => handleSyncIntegration(integration.id)}
                      className="flex-1 px-3 py-2 text-sm rounded text-white"
                      style={{ backgroundColor: colors.primary }}
                    >
                      Sync
                    </button>
                  </div>
                </div>
              ))}
              {integrations.length === 0 && (
                <div className="col-span-full text-center py-12" style={{ color: colors.muted }}>
                  <div className="text-4xl mb-2">🔗</div>
                  <div>No integrations configured</div>
                  <div className="text-sm">Add your first integration to get started</div>
                </div>
              )}
            </div>
          )}

          {/* Webhooks Tab */}
          {activeTab === 'webhooks' && (
            <div className="space-y-4">
              {webhooks.map((webhook) => (
                <div
                  key={webhook.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-medium" style={{ color: colors.text }}>
                        {webhook.name}
                      </h3>
                      <p className="text-sm" style={{ color: colors.muted }}>
                        {webhook.url}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className="text-xs px-2 py-1 rounded"
                        style={{
                          backgroundColor: webhook.enabled ? colors.success + '20' : colors.muted + '20',
                          color: webhook.enabled ? colors.success : colors.muted,
                        }}
                      >
                        {webhook.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                      <button
                        onClick={() => handleTestWebhook(webhook.id)}
                        className="px-3 py-1 text-xs rounded text-white"
                        style={{ backgroundColor: colors.primary }}
                      >
                        Test
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                    <div>
                      <span style={{ color: colors.muted }}>Events:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {webhook.events.length}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Total Deliveries:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {webhook.stats.totalDeliveries}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Success Rate:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {webhook.stats.totalDeliveries > 0
                          ? Math.round((webhook.stats.successfulDeliveries / webhook.stats.totalDeliveries) * 100)
                          : 0}%
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Avg Response:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {webhook.stats.averageResponseTime}ms
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {webhook.events.map((event, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 rounded"
                        style={{ backgroundColor: colors.muted + '20', color: colors.muted }}
                      >
                        {event.type}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
              {webhooks.length === 0 && (
                <div className="text-center py-12" style={{ color: colors.muted }}>
                  <div className="text-4xl mb-2">🪝</div>
                  <div>No webhooks configured</div>
                  <div className="text-sm">Create webhooks to receive real-time notifications</div>
                </div>
              )}
            </div>
          )}

          {/* API Keys Tab */}
          {activeTab === 'api-keys' && (
            <div className="space-y-4">
              {apiKeys.map((apiKey) => (
                <div
                  key={apiKey.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-medium" style={{ color: colors.text }}>
                        {apiKey.name}
                      </h3>
                      <p className="text-sm" style={{ color: colors.muted }}>
                        Created: {apiKey.createdAt.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className="text-xs px-2 py-1 rounded"
                        style={{
                          backgroundColor: apiKey.enabled ? colors.success + '20' : colors.muted + '20',
                          color: apiKey.enabled ? colors.success : colors.muted,
                        }}
                      >
                        {apiKey.enabled ? 'Active' : 'Inactive'}
                      </span>
                      {apiKey.expiresAt && (
                        <span
                          className="text-xs px-2 py-1 rounded"
                          style={{
                            backgroundColor: new Date(apiKey.expiresAt) < new Date() ? colors.error + '20' : colors.warning + '20',
                            color: new Date(apiKey.expiresAt) < new Date() ? colors.error : colors.warning,
                          }}
                        >
                          {new Date(apiKey.expiresAt) < new Date() ? 'Expired' : 'Expires Soon'}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                    <div>
                      <span style={{ color: colors.muted }}>Permissions:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {apiKey.permissions.length}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Total Requests:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {apiKey.usage.totalRequests}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>This Month:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {apiKey.usage.requestsThisMonth}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Error Rate:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {(apiKey.usage.errorRate * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {apiKey.permissions.map((permission, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 rounded"
                        style={{ backgroundColor: colors.primary + '20', color: colors.primary }}
                      >
                        {permission.resource}: {permission.actions.join(', ')}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
              {apiKeys.length === 0 && (
                <div className="text-center py-12" style={{ color: colors.muted }}>
                  <div className="text-4xl mb-2">🔑</div>
                  <div>No API keys created</div>
                  <div className="text-sm">Generate API keys for external applications</div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
