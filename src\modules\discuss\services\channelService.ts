// Channel service for handling channel-related API calls
import type { 
  Channel, 
  User,
  ApiResponse, 
  PaginatedResponse 
} from '../types';

const API_BASE = '/api/discuss';

export interface CreateChannelRequest {
  name: string;
  description?: string;
  type: 'public' | 'private';
  memberIds?: string[];
}

export interface UpdateChannelRequest {
  name?: string;
  description?: string;
  settings?: Partial<Channel['settings']>;
}

export const channelService = {
  // Get all channels for current user
  async getChannels(): Promise<ApiResponse<Channel[]>> {
    const response = await fetch(`${API_BASE}/channels`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch channels');
    }
    
    return response.json();
  },

  // Get channel by ID
  async getChannel(channelId: string): Promise<ApiResponse<Channel>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch channel');
    }
    
    return response.json();
  },

  // Create a new channel
  async createChannel(request: CreateChannelRequest): Promise<ApiResponse<Channel>> {
    const response = await fetch(`${API_BASE}/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create channel');
    }
    
    return response.json();
  },

  // Update channel
  async updateChannel(
    channelId: string, 
    request: UpdateChannelRequest
  ): Promise<ApiResponse<Channel>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update channel');
    }
    
    return response.json();
  },

  // Delete channel
  async deleteChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete channel');
    }
    
    return response.json();
  },

  // Get channel members
  async getChannelMembers(channelId: string): Promise<ApiResponse<User[]>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/members`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch channel members');
    }
    
    return response.json();
  },

  // Add member to channel
  async addMember(channelId: string, userId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to add member to channel');
    }
    
    return response.json();
  },

  // Remove member from channel
  async removeMember(channelId: string, userId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/members/${userId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to remove member from channel');
    }
    
    return response.json();
  },

  // Join channel
  async joinChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/join`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to join channel');
    }
    
    return response.json();
  },

  // Leave channel
  async leaveChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/leave`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to leave channel');
    }
    
    return response.json();
  },

  // Archive channel
  async archiveChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/archive`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to archive channel');
    }
    
    return response.json();
  },

  // Unarchive channel
  async unarchiveChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/unarchive`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to unarchive channel');
    }
    
    return response.json();
  },

  // Mute channel
  async muteChannel(channelId: string, muteUntil?: Date): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/mute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ muteUntil }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to mute channel');
    }
    
    return response.json();
  },

  // Unmute channel
  async unmuteChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/unmute`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to unmute channel');
    }
    
    return response.json();
  },

  // Get channel statistics
  async getChannelStats(channelId: string): Promise<ApiResponse<{
    memberCount: number;
    messageCount: number;
    lastActivity: Date;
    pinnedMessageCount: number;
  }>> {
    const response = await fetch(`${API_BASE}/channels/${channelId}/stats`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch channel statistics');
    }
    
    return response.json();
  },

  // Search channels
  async searchChannels(query: string): Promise<ApiResponse<Channel[]>> {
    const response = await fetch(`${API_BASE}/channels/search?q=${encodeURIComponent(query)}`);
    
    if (!response.ok) {
      throw new Error('Failed to search channels');
    }
    
    return response.json();
  },
};
