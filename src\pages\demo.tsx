import { Link } from 'react-router-dom';
import { useTheme } from '../hooks/useTheme';
import { useAppStore } from '../stores/appStore';
import { ThemeToggle } from '../components/ui';
import { UserList } from '../components/UserList';
import { AddUser } from '../components/AddUser';
import { Button, Card, Input } from '../components/ui';
import { ErrorBoundary } from '../components/common';

export default function DemoPage() {
  const { setTheme, colors } = useTheme();
  // TODO: Use setTheme and colors when implementing theme functionality
  console.log('Theme available:', { setTheme, colors });
  const { configuration } = useAppStore();

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 transition-colors">
      {/* Header */}
      <header className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-slate-900 dark:text-slate-100">
                {configuration.appName || 'Nexed Web'} - Demo
              </h1>
              <div className="hidden sm:flex items-center space-x-2 text-sm text-slate-500 dark:text-slate-400">
                <span>React</span>
                <span>•</span>
                <span>TypeScript</span>
                <span>•</span>
                <span>Tailwind v4</span>
                <span>•</span>
                <span>Zustand</span>
                <span>•</span>
                <span>SWR</span>
                <span>•</span>
                <span>React Router</span>
                <span>•</span>
                <span>MSW</span>
                <span>•</span>
                <span>Vitest</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
              >
                Back to Login
              </Link>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
              Welcome to Your Modern React Setup
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto mb-6">
              This application demonstrates a complete React TypeScript setup
              with Vite, Tailwind CSS v4, Zustand state management, SWR for data
              fetching, React Router for routing, MSW for API mocking, and
              Vitest for testing.
            </p>

            {/* Navigation Links */}
            <div className="flex justify-center space-x-4 mb-8">
              <Link
                to="/dashboard"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                📊 Dashboard
              </Link>
              <Link
                to="/views-demo"
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                👁️ Views Demo
              </Link>
              <Link
                to="/app-header-demo"
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                🎯 Header Demo
              </Link>
              <Link
                to="/login-demo"
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                🔐 Login Demo
              </Link>
            </div>
          </div>

          {/* Component Demo Section */}
          <div className="grid md:grid-cols-2 gap-8">
            <ErrorBoundary
              level="section"
              componentName="ComponentLibraryDemo"
              enableAutoRecovery={true}
              enableReporting={true}
            >
              <Card variant="elevated" padding="lg">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">
                  Component Library Demo
                </h3>
                <div className="space-y-4">
                  <div>
                    <Input
                      label="Sample Input"
                      placeholder="Try typing here..."
                      helperText="This input uses the theme colors"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="primary">Primary</Button>
                    <Button variant="secondary">Secondary</Button>
                    <Button variant="outline">Outline</Button>
                  </div>
                  <Card variant="outlined" padding="sm">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Nested card component with theme integration
                    </p>
                  </Card>
                </div>
              </Card>
            </ErrorBoundary>

            <ErrorBoundary
              level="section"
              componentName="UserManagementDemo"
              enableAutoRecovery={true}
              enableReporting={true}
            >
              <Card variant="elevated" padding="lg">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">
                  User Management Demo
                </h3>
                <p className="text-slate-600 dark:text-slate-400 mb-4">
                  Zustand state management with SWR data fetching and MSW API
                  mocking.
                </p>
                <ErrorBoundary
                  level="component"
                  componentName="AddUser"
                  enableAutoRecovery={true}
                  enableReporting={true}
                >
                  <AddUser />
                </ErrorBoundary>
                <ErrorBoundary
                  level="component"
                  componentName="UserList"
                  enableAutoRecovery={true}
                  enableReporting={true}
                >
                  <UserList />
                </ErrorBoundary>
              </Card>
            </ErrorBoundary>
          </div>

          {/* Navigation Section */}
          <div className="text-center space-y-6">
            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
              Explore the Application
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                to="/dashboard"
                className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 shadow-sm"
              >
                📊 Dashboard
              </Link>
              <Link
                to="/app-header-demo"
                className="inline-flex items-center px-6 py-3 border border-slate-300 dark:border-slate-600 text-sm font-medium rounded-lg text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors duration-200 shadow-sm"
              >
                🎛️ App Header Demo
              </Link>
              <Link
                to="/login-demo"
                className="inline-flex items-center px-6 py-3 border border-slate-300 dark:border-slate-600 text-sm font-medium rounded-lg text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors duration-200 shadow-sm"
              >
                🔐 Login Demo
              </Link>
              <Link
                to="/app?menu=10"
                className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-orange-600 hover:bg-orange-700 transition-colors duration-200 shadow-sm"
              >
                💬 Dynamic App View
              </Link>
            </div>
          </div>

          {/* Features Section */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                title: '⚡ Vite',
                description:
                  'Lightning fast development with HMR and optimized builds',
              },
              {
                title: '🎨 Tailwind CSS v4',
                description:
                  'Modern utility-first CSS framework with dark mode support',
              },
              {
                title: '🐻 Zustand',
                description:
                  'Lightweight state management with TypeScript support',
              },
              {
                title: '🔄 SWR',
                description:
                  'Data fetching with caching, revalidation, and error handling',
              },
              {
                title: '🛣️ React Router',
                description:
                  'Declarative routing with folder-based organization',
              },
              {
                title: '🎭 MSW',
                description:
                  'API mocking for development and testing environments',
              },
              {
                title: '🧪 Vitest',
                description:
                  'Fast unit testing with React Testing Library integration',
              },
              {
                title: '🪝 Husky',
                description:
                  'Git hooks for code quality with ESLint and Prettier',
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow"
              >
                <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                  {feature.title}
                </h4>
                <p className="text-slate-600 dark:text-slate-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
