// Hook for managing voice/video calls
import { useState, useEffect, useCallback, useRef } from 'react';
import { callService } from '../services/callService';
import { webrtcService } from '../services/webrtcService';
import { websocketService } from '../services/websocketService';
import type { Call, CallParticipant } from '../types';
import type { StartCallRequest, CallSettings } from '../services/callService';
import type { MediaConstraints, PeerConnection } from '../services/webrtcService';

export interface UseCallOptions {
  userId: string;
  channelId?: string;
  autoJoin?: boolean;
}

export interface UseCallReturn {
  // Call state
  currentCall: Call | null;
  participants: CallParticipant[];
  isInCall: boolean;
  isConnecting: boolean;
  callError: string | null;
  
  // Media state
  localStream: MediaStream | null;
  remoteStreams: Map<string, MediaStream>;
  isAudioEnabled: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
  
  // Call actions
  startCall: (type: 'voice' | 'video', participantIds: string[]) => Promise<void>;
  joinCall: (callId: string) => Promise<void>;
  leaveCall: () => Promise<void>;
  endCall: () => Promise<void>;
  
  // Media controls
  toggleAudio: () => void;
  toggleVideo: () => void;
  startScreenShare: () => Promise<void>;
  stopScreenShare: () => void;
  
  // Call management
  inviteParticipant: (userId: string) => Promise<void>;
  removeParticipant: (userId: string) => Promise<void>;
  
  // Recording
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  isRecording: boolean;
}

export const useCall = (options: UseCallOptions): UseCallReturn => {
  const { userId, channelId, autoJoin = false } = options;
  
  // Call state
  const [currentCall, setCurrentCall] = useState<Call | null>(null);
  const [participants, setParticipants] = useState<CallParticipant[]>([]);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callError, setCallError] = useState<string | null>(null);
  
  // Media state
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStreams, setRemoteStreams] = useState<Map<string, MediaStream>>(new Map());
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  
  // Refs
  const recordingIdRef = useRef<string | null>(null);
  const peersRef = useRef<Map<string, PeerConnection>>(new Map());

  // Computed state
  const isInCall = currentCall !== null && currentCall.status === 'active';

  // Initialize WebRTC event handlers
  useEffect(() => {
    const handlePeerConnected = ({ peerId, userId: peerUserId }: any) => {
      console.log('Peer connected:', peerId, peerUserId);
    };

    const handleStreamReceived = ({ peerId, userId: peerUserId, stream }: any) => {
      setRemoteStreams(prev => new Map(prev.set(peerUserId, stream)));
    };

    const handlePeerDisconnected = ({ peerId, userId: peerUserId }: any) => {
      setRemoteStreams(prev => {
        const newMap = new Map(prev);
        newMap.delete(peerUserId);
        return newMap;
      });
    };

    const handleError = ({ error }: any) => {
      setCallError(error.message || 'WebRTC error occurred');
    };

    webrtcService.on('peer-connected', handlePeerConnected);
    webrtcService.on('stream-received', handleStreamReceived);
    webrtcService.on('peer-disconnected', handlePeerDisconnected);
    webrtcService.on('error', handleError);

    return () => {
      webrtcService.off('peer-connected', handlePeerConnected);
      webrtcService.off('stream-received', handleStreamReceived);
      webrtcService.off('peer-disconnected', handlePeerDisconnected);
      webrtcService.off('error', handleError);
    };
  }, []);

  // WebSocket event handlers for call signaling
  useEffect(() => {
    const handleCallInvite = (data: any) => {
      if (autoJoin) {
        joinCall(data.callId);
      }
    };

    const handleCallSignal = (data: any) => {
      const { peerId, signal } = data;
      webrtcService.signalPeer(peerId, signal);
    };

    const handleCallEnded = () => {
      cleanup();
    };

    websocketService.on('call-invite', handleCallInvite);
    websocketService.on('call-signal', handleCallSignal);
    websocketService.on('call-ended', handleCallEnded);

    return () => {
      websocketService.off('call-invite', handleCallInvite);
      websocketService.off('call-signal', handleCallSignal);
      websocketService.off('call-ended', handleCallEnded);
    };
  }, [autoJoin]);

  // Start a new call
  const startCall = useCallback(async (type: 'voice' | 'video', participantIds: string[]) => {
    try {
      setIsConnecting(true);
      setCallError(null);

      // Initialize media
      const constraints: MediaConstraints = {
        audio: true,
        video: type === 'video',
      };
      
      const stream = await webrtcService.initializeMedia(constraints);
      setLocalStream(stream);
      setIsVideoEnabled(type === 'video');

      // Start call via API
      const request: StartCallRequest = {
        type,
        channelId,
        participantIds,
      };

      const response = await callService.startCall(request);
      setCurrentCall(response.data);

      // Create peer connections for each participant
      participantIds.forEach(participantId => {
        const peer = webrtcService.createPeer(participantId, true, stream);
        peersRef.current.set(participantId, peer);
      });

    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to start call');
    } finally {
      setIsConnecting(false);
    }
  }, [channelId]);

  // Join an existing call
  const joinCall = useCallback(async (callId: string) => {
    try {
      setIsConnecting(true);
      setCallError(null);

      // Get call details
      const callResponse = await callService.getCall(callId);
      const call = callResponse.data;
      setCurrentCall(call);

      // Initialize media
      const constraints: MediaConstraints = {
        audio: true,
        video: call.type === 'video',
      };
      
      const stream = await webrtcService.initializeMedia(constraints);
      setLocalStream(stream);
      setIsVideoEnabled(call.type === 'video');

      // Join call via API
      const joinResponse = await callService.joinCall({
        callId,
        userId,
        mediaConstraints: constraints,
      });

      // Get participants and create peer connections
      const participantsResponse = await callService.getCallParticipants(callId);
      setParticipants(participantsResponse.data);

      participantsResponse.data.forEach(participant => {
        if (participant.userId !== userId) {
          const peer = webrtcService.createPeer(participant.userId, false, stream);
          peersRef.current.set(participant.userId, peer);
        }
      });

    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to join call');
    } finally {
      setIsConnecting(false);
    }
  }, [userId]);

  // Leave the current call
  const leaveCall = useCallback(async () => {
    if (!currentCall) return;

    try {
      await callService.leaveCall(currentCall.id, userId);
      cleanup();
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to leave call');
    }
  }, [currentCall, userId]);

  // End the current call (only initiator can do this)
  const endCall = useCallback(async () => {
    if (!currentCall) return;

    try {
      await callService.endCall(currentCall.id);
      cleanup();
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to end call');
    }
  }, [currentCall]);

  // Toggle audio on/off
  const toggleAudio = useCallback(() => {
    const newState = !isAudioEnabled;
    setIsAudioEnabled(newState);
    webrtcService.toggleAudio(newState);
    
    if (currentCall) {
      callService.updateCallSettings(currentCall.id, userId, { isMuted: !newState });
    }
  }, [isAudioEnabled, currentCall, userId]);

  // Toggle video on/off
  const toggleVideo = useCallback(() => {
    const newState = !isVideoEnabled;
    setIsVideoEnabled(newState);
    webrtcService.toggleVideo(newState);
    
    if (currentCall) {
      callService.updateCallSettings(currentCall.id, userId, { isVideoEnabled: newState });
    }
  }, [isVideoEnabled, currentCall, userId]);

  // Start screen sharing
  const startScreenShare = useCallback(async () => {
    try {
      await webrtcService.startScreenShare();
      setIsScreenSharing(true);
      
      if (currentCall) {
        await callService.updateCallSettings(currentCall.id, userId, { isScreenSharing: true });
      }
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to start screen sharing');
    }
  }, [currentCall, userId]);

  // Stop screen sharing
  const stopScreenShare = useCallback(async () => {
    try {
      await webrtcService.stopScreenShare();
      setIsScreenSharing(false);
      
      if (currentCall) {
        await callService.updateCallSettings(currentCall.id, userId, { isScreenSharing: false });
      }
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to stop screen sharing');
    }
  }, [currentCall, userId]);

  // Start recording
  const startRecording = useCallback(async () => {
    if (!currentCall) return;

    try {
      const response = await callService.startRecording(currentCall.id);
      recordingIdRef.current = response.data.recordingId;
      setIsRecording(true);
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to start recording');
    }
  }, [currentCall]);

  // Stop recording
  const stopRecording = useCallback(async () => {
    if (!currentCall || !recordingIdRef.current) return;

    try {
      await callService.stopRecording(currentCall.id, recordingIdRef.current);
      recordingIdRef.current = null;
      setIsRecording(false);
    } catch (error) {
      setCallError(error instanceof Error ? error.message : 'Failed to stop recording');
    }
  }, [currentCall]);

  // Invite participant (placeholder)
  const inviteParticipant = useCallback(async (participantUserId: string) => {
    // Implementation would depend on your invitation system
    console.log('Inviting participant:', participantUserId);
  }, []);

  // Remove participant (placeholder)
  const removeParticipant = useCallback(async (participantUserId: string) => {
    // Implementation would depend on your permission system
    console.log('Removing participant:', participantUserId);
  }, []);

  // Cleanup function
  const cleanup = useCallback(() => {
    setCurrentCall(null);
    setParticipants([]);
    setLocalStream(null);
    setRemoteStreams(new Map());
    setIsAudioEnabled(true);
    setIsVideoEnabled(true);
    setIsScreenSharing(false);
    setIsRecording(false);
    setCallError(null);
    
    webrtcService.disconnect();
    peersRef.current.clear();
    recordingIdRef.current = null;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    // Call state
    currentCall,
    participants,
    isInCall,
    isConnecting,
    callError,
    
    // Media state
    localStream,
    remoteStreams,
    isAudioEnabled,
    isVideoEnabled,
    isScreenSharing,
    
    // Call actions
    startCall,
    joinCall,
    leaveCall,
    endCall,
    
    // Media controls
    toggleAudio,
    toggleVideo,
    startScreenShare,
    stopScreenShare,
    
    // Call management
    inviteParticipant,
    removeParticipant,
    
    // Recording
    startRecording,
    stopRecording,
    isRecording,
  };
};
