import { BrowserRouter } from 'react-router-dom';
import DynamicAppBottomBar from './DynamicAppBottomBar';
import { GridIcon, ListIcon, BarChartIcon, SearchIcon } from '../../icons';

// Mock Storybook types for demonstration
type Meta<T> = {
  title: string;
  component: React.ComponentType<any>;
  parameters?: any;
  decorators?: any[];
  argTypes?: any;
};

type StoryObj<T> = {
  args: T;
};

const meta: Meta<any> = {
  title: 'Layout/DynamicAppBottomBar',
  component: DynamicAppBottomBar,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A standalone bottom bar component for contextual controls. This component was separated from DynamicAppHeader for better extendability and reduced complexity.',
      },
    },
  },
  decorators: [
    (Story: any) => (
      <BrowserRouter>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="px-4 sm:px-6 lg:px-8 py-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Sample Application Header
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                This shows how the bottom bar works as a standalone component
              </p>
            </div>
          </div>
          <Story />
          <div className="p-8">
            <div className="max-w-4xl mx-auto space-y-6">
              {Array.from({ length: 5 }, (_, i) => (
                <div
                  key={i}
                  className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Content Item {i + 1}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    This demonstrates how the bottom bar provides contextual controls
                    for the content below. The component is now completely separate
                    and can be used independently.
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </BrowserRouter>
    ),
  ],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<any>;

// Mock view data
const mockView = {
  title: 'Projects Dashboard',
  breadcrumbs: [
    { label: 'Home', onClick: () => console.log('Home clicked') },
    { label: 'Workspace', onClick: () => console.log('Workspace clicked') },
    { label: 'Projects', onClick: () => console.log('Projects clicked') },
  ],
  actions: [
    { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true, variant: 'filled' as const },
    { label: 'Upload', onClick: () => console.log('Upload clicked'), variant: 'outline' as const },
    { label: 'Export', onClick: () => console.log('Export clicked') },
  ],
  search: {
    filterTags: [
      { id: '1', label: 'Active', color: 'blue' },
      { id: '2', label: 'High Priority', color: 'red' },
    ],
    filterItems: [
      { id: 'status', label: 'Status', options: ['Active', 'Completed', 'On Hold'] },
      { id: 'priority', label: 'Priority', options: ['High', 'Medium', 'Low'] },
    ],
    groupByItems: [
      { id: 'status', label: 'Status' },
      { id: 'assignee', label: 'Assignee' },
      { id: 'priority', label: 'Priority' },
    ],
    favoriteItems: [
      { id: '1', label: 'My Active Projects' },
      { id: '2', label: 'High Priority Tasks' },
    ],
    onSearch: (query: string) => console.log('Search:', query),
    onTagRemove: (tagId: string) => console.log('Remove tag:', tagId),
    onFilterSelect: (filterId: string) => console.log('Filter select:', filterId),
    onGroupBySelect: (groupId: string) => console.log('Group by:', groupId),
    onFavoriteSelect: (favoriteId: string) => console.log('Favorite select:', favoriteId),
    onFavoriteDelete: (favoriteId: string) => console.log('Favorite delete:', favoriteId),
    onAddCustomFilter: () => console.log('Add custom filter'),
    onAddCustomGroup: () => console.log('Add custom group'),
    onSaveCurrentSearch: () => console.log('Save current search'),
  },
  pagination: {
    currentRange: '1-20 of 156',
    onNext: () => console.log('Next page'),
    onPrev: () => console.log('Previous page'),
  },
  viewModes: [
    { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
    { name: 'list', icon: <ListIcon className="w-4 h-4" /> },
    { name: 'chart', icon: <BarChartIcon className="w-4 h-4" /> },
    { name: 'search', icon: <SearchIcon className="w-4 h-4" /> },
  ],
  activeViewMode: 'grid',
};

export const Default: Story = {
  args: {
    view: mockView,
  },
};

export const WithManyActions: Story = {
  args: {
    view: {
      ...mockView,
      actions: [
        { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true, variant: 'filled' as const },
        { label: 'Upload', onClick: () => console.log('Upload clicked'), variant: 'outline' as const },
        { label: 'Export', onClick: () => console.log('Export clicked') },
        { label: 'Import', onClick: () => console.log('Import clicked') },
        { label: 'Archive', onClick: () => console.log('Archive clicked') },
        { label: 'Settings', onClick: () => console.log('Settings clicked') },
      ],
    },
  },
};

export const WithLongTitle: Story = {
  args: {
    view: {
      ...mockView,
      title: 'Customer Relationship Management Dashboard with Extended Analytics',
    },
  },
};

export const MinimalConfiguration: Story = {
  args: {
    view: {
      title: 'Simple View',
      actions: [
        { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true, variant: 'filled' as const },
      ],
      search: {
        onSearch: (query: string) => console.log('Search:', query),
      },
      pagination: {
        currentRange: '1-10 of 10',
        onNext: () => console.log('Next page'),
        onPrev: () => console.log('Previous page'),
      },
      viewModes: [
        { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
      ],
      activeViewMode: 'grid',
    },
  },
};

export const WithManyFilters: Story = {
  args: {
    view: {
      ...mockView,
      search: {
        ...mockView.search,
        filterTags: [
          { id: '1', label: 'Active Projects', color: 'blue' },
          { id: '2', label: 'High Priority', color: 'red' },
          { id: '3', label: 'Due This Week', color: 'orange' },
          { id: '4', label: 'Assigned to Me', color: 'green' },
          { id: '5', label: 'Client Review', color: 'purple' },
        ],
      },
    },
  },
};

export const DifferentViewMode: Story = {
  args: {
    view: {
      ...mockView,
      activeViewMode: 'list',
    },
  },
};

export const WithBreadcrumbs: Story = {
  args: {
    view: {
      ...mockView,
      title: 'Task Details',
      breadcrumbs: [
        { label: 'Dashboard', onClick: () => console.log('Dashboard clicked') },
        { label: 'Projects', onClick: () => console.log('Projects clicked') },
        { label: 'Web Development', onClick: () => console.log('Web Development clicked') },
        { label: 'Sprint 1', onClick: () => console.log('Sprint 1 clicked') },
      ],
    },
  },
};

export const WithLongBreadcrumbs: Story = {
  args: {
    view: {
      ...mockView,
      title: 'Very Long Task Name That Should Be Truncated',
      breadcrumbs: [
        { label: 'Dashboard', onClick: () => console.log('Dashboard clicked') },
        { label: 'Customer Relationship Management', onClick: () => console.log('CRM clicked') },
        { label: 'Enterprise Solutions Division', onClick: () => console.log('Division clicked') },
        { label: 'Q4 2024 Strategic Planning', onClick: () => console.log('Planning clicked') },
        { label: 'Implementation Phase', onClick: () => console.log('Implementation clicked') },
      ],
    },
  },
};

export const WithoutBreadcrumbs: Story = {
  args: {
    view: {
      ...mockView,
      breadcrumbs: undefined,
      title: 'Simple Dashboard',
    },
  },
};

export const BreadcrumbsOnly: Story = {
  args: {
    view: {
      ...mockView,
      actions: [],
      title: 'Settings',
      breadcrumbs: [
        { label: 'Home', onClick: () => console.log('Home clicked') },
        { label: 'Administration', onClick: () => console.log('Admin clicked') },
      ],
    },
  },
};

export const WithConfirmation: Story = {
  args: {
    view: {
      title: 'User Management',
      breadcrumbs: [
        { label: 'Admin', onClick: () => console.log('Admin clicked') },
        { label: 'Users', onClick: () => console.log('Users clicked') },
      ],
      actions: [
        { label: 'Add User', onClick: () => console.log('Add User'), isPrimary: true, variant: 'filled' as const },
        {
          label: 'Delete User',
          onClick: () => console.log('Delete User confirmed'),
          variant: 'outline' as const,
          confirm: {
            title: 'Delete User',
            message: 'Are you sure you want to delete this user? This action cannot be undone.',
            confirmText: 'Delete',
            cancelText: 'Cancel'
          }
        },
        {
          label: 'Reset Password',
          onClick: () => console.log('Password reset'),
          confirm: {
            title: 'Reset Password',
            message: 'This will send a password reset email to the user.'
          }
        },
      ],
      search: {
        onSearch: (query: string) => console.log('Search:', query),
      },
      pagination: {
        currentRange: '1-15 of 45',
        onNext: () => console.log('Next page'),
        onPrev: () => console.log('Previous page'),
      },
      viewModes: [
        { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
        { name: 'list', icon: <ListIcon className="w-4 h-4" /> },
      ],
      activeViewMode: 'grid',
    },
  },
};

export const VariantShowcase: Story = {
  args: {
    view: {
      title: 'Button Variants',
      actions: [
        { label: 'Filled Primary', onClick: () => console.log('Filled Primary'), isPrimary: true, variant: 'filled' as const },
        { label: 'Outline', onClick: () => console.log('Outline'), variant: 'outline' as const },
        { label: 'Default', onClick: () => console.log('Default') },
      ],
      search: {
        onSearch: (query: string) => console.log('Search:', query),
      },
      pagination: {
        currentRange: '1-10 of 10',
        onNext: () => console.log('Next page'),
        onPrev: () => console.log('Previous page'),
      },
      viewModes: [
        { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
      ],
      activeViewMode: 'grid',
    },
  },
};