import React, { useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { ActivityViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface ActivityItem {
  id: string;
  timestamp: Date;
  title: string;
  description?: string;
  user?: string;
  type?: string;
  icon?: React.ReactNode;
  color?: string;
  data: any;
}

export interface ActivityViewProps extends BaseViewProps {
  config: ActivityViewConfig;
  groupBy?: 'date' | 'user' | 'type' | 'none';
  showTimeline?: boolean;
  onActivityClick?: (activity: ActivityItem) => void;
  renderActivity?: (activity: ActivityItem) => React.ReactNode;
  renderGroupHeader?: (
    groupKey: string,
    activities: ActivityItem[]
  ) => React.ReactNode;
}

export const ActivityView: React.FC<ActivityViewProps> = ({
  data,
  config,
  groupBy = config.groupBy || 'date',
  showTimeline = config.showTimeline ?? true,
  onActivityClick,
  renderActivity,
  renderGroupHeader,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  // Convert data to activity items
  const activities = useMemo((): ActivityItem[] => {
    return data.map((item, index) => ({
      id: item.id || index.toString(),
      timestamp: new Date(item[config.timestampField]),
      title: item[config.titleField] || 'Untitled Activity',
      description: config.descriptionField
        ? item[config.descriptionField]
        : undefined,
      user: config.userField ? item[config.userField] : undefined,
      type: config.typeField ? item[config.typeField] : undefined,
      icon: config.activityTypes?.[item[config.typeField!]]?.icon,
      color:
        config.activityTypes?.[item[config.typeField!]]?.color ||
        colors.primary,
      data: item,
    }));
  }, [data, config, colors.primary]);

  // Group activities
  const groupedActivities = useMemo(() => {
    if (groupBy === 'none') {
      return {
        'All Activities': activities.sort(
          (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
        ),
      };
    }

    const groups: { [key: string]: ActivityItem[] } = {};

    activities.forEach(activity => {
      let groupKey: string;

      switch (groupBy) {
        case 'date':
          groupKey = activity.timestamp.toDateString();
          break;
        case 'user':
          groupKey = activity.user || 'Unknown User';
          break;
        case 'type':
          groupKey = activity.type || 'Unknown Type';
          break;
        default:
          groupKey = 'All Activities';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(activity);
    });

    // Sort activities within each group
    Object.keys(groups).forEach(key => {
      groups[key].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    });

    return groups;
  }, [activities, groupBy]);

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return timestamp.toLocaleDateString();
  };

  const renderActivityItem = (
    activity: ActivityItem,
    index: number,
    isLast: boolean
  ) => {
    if (renderActivity) {
      return renderActivity(activity);
    }

    return (
      <div
        className={cn(
          'relative flex items-start space-x-4 pb-6',
          !isLast && showTimeline && 'border-l-2 ml-4',
          onActivityClick &&
            'cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2'
        )}
        style={{
          borderLeftColor: showTimeline ? colors.border : 'transparent',
        }}
        onClick={() => onActivityClick?.(activity)}
      >
        {/* Timeline dot */}
        {showTimeline && (
          <div
            className="absolute left-0 w-4 h-4 rounded-full border-2 -ml-2 mt-1"
            style={{
              backgroundColor: activity.color,
              borderColor: colors.background,
            }}
          />
        )}

        {/* Activity icon */}
        <div
          className="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center"
          style={{ backgroundColor: activity.color }}
        >
          {activity.icon || (
            <span className="text-white text-sm font-semibold">
              {activity.type?.charAt(0).toUpperCase() || '•'}
            </span>
          )}
        </div>

        {/* Activity content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4
              className="text-sm font-medium truncate"
              style={{ color: colors.text }}
            >
              {activity.title}
            </h4>
            <span
              className="text-xs flex-shrink-0 ml-2"
              style={{ color: colors.textSecondary }}
            >
              {formatTimestamp(activity.timestamp)}
            </span>
          </div>

          {activity.description && (
            <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
              {activity.description}
            </p>
          )}

          {activity.user && (
            <div className="flex items-center mt-2 space-x-2">
              <div
                className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
                style={{ backgroundColor: colors.muted, color: colors.text }}
              >
                {activity.user.charAt(0).toUpperCase()}
              </div>
              <span className="text-xs" style={{ color: colors.textSecondary }}>
                {activity.user}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderGroup = (groupKey: string, groupActivities: ActivityItem[]) => (
    <div key={groupKey} className="mb-8">
      {/* Group header */}
      {renderGroupHeader ? (
        renderGroupHeader(groupKey, groupActivities)
      ) : (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
            {groupKey}
          </h3>
          <span
            className="text-sm px-2 py-1 rounded-full"
            style={{
              backgroundColor: colors.muted,
              color: colors.textSecondary,
            }}
          >
            {groupActivities.length}{' '}
            {groupActivities.length === 1 ? 'activity' : 'activities'}
          </span>
        </div>
      )}

      {/* Activities */}
      <div className="space-y-0">
        {groupActivities.map((activity, index) =>
          renderActivityItem(
            activity,
            index,
            index === groupActivities.length - 1
          )
        )}
      </div>
    </div>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-6">
        {Object.entries(groupedActivities).map(([groupKey, groupActivities]) =>
          renderGroup(groupKey, groupActivities)
        )}
      </div>
    </BaseView>
  );
};

export default ActivityView;
