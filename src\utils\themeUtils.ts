import type { Theme, ThemeVariant, ThemeColors } from '../stores/themeStore';

// Color manipulation utilities
export class ColorUtils {
  /**
   * Convert hex color to RGB values
   */
  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  /**
   * Convert RGB values to hex color
   */
  static rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  }

  /**
   * Calculate luminance of a color
   */
  static getLuminance(hex: string): number {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return 0;

    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getLuminance(color1);
    const lum2 = this.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  }

  /**
   * Check if color combination meets WCAG accessibility standards
   */
  static isAccessible(
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA'
  ): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  }

  /**
   * Lighten a color by a percentage
   */
  static lighten(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = Math.round(255 * (percent / 100));

    return this.rgbToHex(
      Math.min(255, r + amount),
      Math.min(255, g + amount),
      Math.min(255, b + amount)
    );
  }

  /**
   * Darken a color by a percentage
   */
  static darken(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = Math.round(255 * (percent / 100));

    return this.rgbToHex(
      Math.max(0, r - amount),
      Math.max(0, g - amount),
      Math.max(0, b - amount)
    );
  }

  /**
   * Add alpha transparency to a hex color
   */
  static addAlpha(hex: string, alpha: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  /**
   * Generate a color palette from a base color
   */
  static generatePalette(baseColor: string): Record<string, string> {
    return {
      50: this.lighten(baseColor, 45),
      100: this.lighten(baseColor, 35),
      200: this.lighten(baseColor, 25),
      300: this.lighten(baseColor, 15),
      400: this.lighten(baseColor, 5),
      500: baseColor,
      600: this.darken(baseColor, 5),
      700: this.darken(baseColor, 15),
      800: this.darken(baseColor, 25),
      900: this.darken(baseColor, 35),
      950: this.darken(baseColor, 45),
    };
  }
}

// Theme detection utilities
export class ThemeDetection {
  /**
   * Detect system theme preference
   */
  static getSystemTheme(): 'light' | 'dark' {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }

  /**
   * Detect if user prefers reduced motion
   */
  static prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Detect if user prefers high contrast
   */
  static prefersHighContrast(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-contrast: high)').matches;
  }

  /**
   * Get recommended theme variant based on user preferences
   */
  static getRecommendedVariant(): ThemeVariant {
    if (this.prefersHighContrast()) {
      return 'high-contrast';
    }
    return 'default';
  }
}

// Theme validation utilities
export class ThemeValidation {
  /**
   * Validate theme colors for accessibility
   */
  static validateThemeColors(colors: ThemeColors): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check primary color contrast
    if (!ColorUtils.isAccessible(colors.primaryForeground, colors.primary)) {
      issues.push(
        'Primary color does not meet accessibility contrast requirements'
      );
      suggestions.push(
        'Consider adjusting primary or primaryForeground colors'
      );
    }

    // Check text contrast
    if (!ColorUtils.isAccessible(colors.text, colors.background)) {
      issues.push(
        'Text color does not meet accessibility contrast requirements'
      );
      suggestions.push('Consider adjusting text or background colors');
    }

    // Check secondary text contrast
    if (!ColorUtils.isAccessible(colors.textSecondary, colors.background)) {
      issues.push('Secondary text color may be too light for accessibility');
      suggestions.push('Consider darkening textSecondary color');
    }

    // Check error color contrast
    if (!ColorUtils.isAccessible(colors.errorForeground, colors.error)) {
      issues.push(
        'Error color does not meet accessibility contrast requirements'
      );
      suggestions.push('Consider adjusting error or errorForeground colors');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
    };
  }

  /**
   * Validate theme configuration
   */
  static validateThemeConfig(
    theme: Theme,
    variant: ThemeVariant
  ): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];

    if (theme === 'system' && typeof window === 'undefined') {
      warnings.push(
        'System theme detection not available in server environment'
      );
    }

    if (variant === 'high-contrast' && !ThemeDetection.prefersHighContrast()) {
      warnings.push(
        'High contrast variant selected but user does not prefer high contrast'
      );
    }

    return {
      isValid: warnings.length === 0,
      warnings,
    };
  }
}

// CSS utilities
export class CSSUtils {
  /**
   * Generate CSS custom properties from theme colors
   */
  static generateCSSVariables(colors: ThemeColors, prefix = '--color'): string {
    return Object.entries(colors)
      .map(([key, value]) => {
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
        return `  ${prefix}-${cssKey}: ${value};`;
      })
      .join('\n');
  }

  /**
   * Generate CSS class for theme
   */
  static generateThemeClass(
    theme: Theme,
    variant: ThemeVariant,
    colors: ThemeColors
  ): string {
    const className = variant === 'default' ? theme : `${theme}-${variant}`;
    const variables = this.generateCSSVariables(colors);

    return `.theme-${className} {\n${variables}\n}`;
  }

  /**
   * Apply CSS variables to an element
   */
  static applyCSSVariables(
    element: HTMLElement,
    variables: Record<string, string>
  ): void {
    Object.entries(variables).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
  }

  /**
   * Remove CSS variables from an element
   */
  static removeCSSVariables(element: HTMLElement, variables: string[]): void {
    variables.forEach(property => {
      element.style.removeProperty(property);
    });
  }
}

// Theme persistence utilities
export class ThemePersistence {
  /**
   * Save theme to localStorage
   */
  static saveTheme(
    theme: Theme,
    variant: ThemeVariant,
    key = 'app-theme'
  ): void {
    if (typeof window === 'undefined') return;

    try {
      const themeData = { theme, variant, timestamp: Date.now() };
      localStorage.setItem(key, JSON.stringify(themeData));
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }

  /**
   * Load theme from localStorage
   */
  static loadTheme(
    key = 'app-theme'
  ): { theme: Theme; variant: ThemeVariant } | null {
    if (typeof window === 'undefined') return null;

    try {
      const stored = localStorage.getItem(key);
      if (!stored) return null;

      const themeData = JSON.parse(stored);
      return {
        theme: themeData.theme || 'system',
        variant: themeData.variant || 'default',
      };
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear theme from localStorage
   */
  static clearTheme(key = 'app-theme'): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to clear theme from localStorage:', error);
    }
  }
}

// Animation utilities for theme transitions
export class ThemeAnimations {
  /**
   * Create smooth theme transition
   */
  static createThemeTransition(duration = 300): void {
    const style = document.createElement('style');
    style.textContent = `
      * {
        transition: background-color ${duration}ms ease-in-out,
                   color ${duration}ms ease-in-out,
                   border-color ${duration}ms ease-in-out,
                   box-shadow ${duration}ms ease-in-out !important;
      }
    `;

    document.head.appendChild(style);

    setTimeout(() => {
      document.head.removeChild(style);
    }, duration);
  }

  /**
   * Animate theme change with fade effect
   */
  static animateThemeChange(callback: () => void, duration = 200): void {
    document.body.style.transition = `opacity ${duration}ms ease-in-out`;
    document.body.style.opacity = '0.8';

    setTimeout(() => {
      callback();
      document.body.style.opacity = '1';

      setTimeout(() => {
        document.body.style.transition = '';
      }, duration);
    }, duration / 2);
  }
}
