import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Message, User } from '../../types';

export interface DeliveryStatusProps {
  message: Message;
  readBy?: Array<{ user: User; readAt: Date }>;
  showReadReceipts?: boolean;
  size?: 'small' | 'medium';
  className?: string;
  'data-testid'?: string;
}

export const DeliveryStatus: React.FC<DeliveryStatusProps> = ({
  message,
  readBy = [],
  showReadReceipts = true,
  size = 'small',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getStatusIcon = (status: Message['deliveryStatus']) => {
    switch (status) {
      case 'sent':
        return '✓';
      case 'delivered':
        return '✓✓';
      case 'read':
        return '✓✓';
      case 'failed':
        return '✗';
      default:
        return '○';
    }
  };

  const getStatusColor = (status: Message['deliveryStatus']) => {
    switch (status) {
      case 'sent':
        return colors.textSecondary;
      case 'delivered':
        return colors.textSecondary;
      case 'read':
        return colors.primary;
      case 'failed':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusText = (status: Message['deliveryStatus']) => {
    switch (status) {
      case 'sent':
        return 'Sent';
      case 'delivered':
        return 'Delivered';
      case 'read':
        return 'Read';
      case 'failed':
        return 'Failed to send';
      default:
        return 'Sending...';
    }
  };

  const formatReadTime = (readAt: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - readAt.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    return readAt.toLocaleDateString();
  };

  const iconSize = size === 'small' ? 'text-xs' : 'text-sm';
  const textSize = size === 'small' ? 'text-xs' : 'text-sm';

  return (
    <div className={`flex items-center space-x-2 ${className}`} data-testid={testId}>
      {/* Delivery Status Icon */}
      <span
        className={`${iconSize} font-mono`}
        style={{ color: getStatusColor(message.deliveryStatus) }}
        title={getStatusText(message.deliveryStatus)}
      >
        {getStatusIcon(message.deliveryStatus)}
      </span>

      {/* Read Receipts */}
      {showReadReceipts && readBy.length > 0 && (
        <div className="flex items-center space-x-1">
          {/* Reader Avatars */}
          <div className="flex -space-x-1">
            {readBy.slice(0, 3).map(({ user }) => (
              <div
                key={user.id}
                className={`${size === 'small' ? 'w-4 h-4' : 'w-5 h-5'} rounded-full border-2 border-white flex items-center justify-center text-white text-xs font-semibold`}
                style={{ backgroundColor: colors.primary }}
                title={`Read by ${user.name}`}
              >
                {user.avatar || user.name.charAt(0).toUpperCase()}
              </div>
            ))}
            {readBy.length > 3 && (
              <div
                className={`${size === 'small' ? 'w-4 h-4' : 'w-5 h-5'} rounded-full border-2 border-white flex items-center justify-center text-xs font-semibold`}
                style={{ 
                  backgroundColor: colors.textSecondary,
                  color: colors.background,
                }}
                title={`+${readBy.length - 3} more`}
              >
                +{readBy.length - 3}
              </div>
            )}
          </div>

          {/* Read Count */}
          {readBy.length === 1 ? (
            <span className={`${textSize}`} style={{ color: colors.textSecondary }}>
              Read by {readBy[0].user.name}
            </span>
          ) : (
            <span className={`${textSize}`} style={{ color: colors.textSecondary }}>
              Read by {readBy.length}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export interface ReadReceiptTooltipProps {
  readBy: Array<{ user: User; readAt: Date }>;
  isVisible: boolean;
  position?: { top: number; left: number };
  className?: string;
  'data-testid'?: string;
}

export const ReadReceiptTooltip: React.FC<ReadReceiptTooltipProps> = ({
  readBy,
  isVisible,
  position = { top: 0, left: 0 },
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (!isVisible || readBy.length === 0) return null;

  return (
    <div
      className={`absolute z-50 p-3 rounded-lg shadow-lg border max-w-xs ${className}`}
      style={{
        top: position.top,
        left: position.left,
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      <div className="space-y-2">
        <h4 className="font-medium text-sm" style={{ color: colors.text }}>
          Read by {readBy.length} {readBy.length === 1 ? 'person' : 'people'}
        </h4>
        
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {readBy.map(({ user, readAt }) => (
            <div key={user.id} className="flex items-center justify-between space-x-3">
              <div className="flex items-center space-x-2">
                <div
                  className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-semibold"
                  style={{ backgroundColor: colors.primary }}
                >
                  {user.avatar || user.name.charAt(0).toUpperCase()}
                </div>
                <span className="text-sm" style={{ color: colors.text }}>
                  {user.name}
                </span>
              </div>
              <span className="text-xs" style={{ color: colors.textSecondary }}>
                {formatReadTime(readAt)}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export interface MessageStatusIndicatorProps {
  message: Message;
  showDeliveryStatus?: boolean;
  showReadReceipts?: boolean;
  readBy?: Array<{ user: User; readAt: Date }>;
  className?: string;
  'data-testid'?: string;
}

export const MessageStatusIndicator: React.FC<MessageStatusIndicatorProps> = ({
  message,
  showDeliveryStatus = true,
  showReadReceipts = true,
  readBy = [],
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  // Don't show status for old messages or messages from others
  const showStatus = showDeliveryStatus && message.deliveryStatus !== 'read';
  const showReceipts = showReadReceipts && readBy.length > 0;

  if (!showStatus && !showReceipts) return null;

  return (
    <div className={`flex items-center justify-end space-x-2 mt-1 ${className}`} data-testid={testId}>
      {/* Timestamp */}
      <span className="text-xs" style={{ color: colors.textSecondary }}>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </span>

      {/* Delivery Status */}
      {showStatus && (
        <DeliveryStatus
          message={message}
          readBy={readBy}
          showReadReceipts={showReceipts}
          size="small"
        />
      )}
    </div>
  );
};

// Helper function for formatting read time
const formatReadTime = (readAt: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - readAt.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  return readAt.toLocaleDateString();
};
