@import 'tailwindcss';

/* Root CSS variables for theme colors */
:root {
  /* Default light theme colors - will be overridden by Theme<PERSON>rovider */
  --color-primary: #2563eb;
  --color-primary-foreground: #ffffff;
  --color-secondary: #4f46e5;
  --color-secondary-foreground: #ffffff;
  --color-accent: #7c3aed;
  --color-accent-foreground: #ffffff;
  --color-neutral: #6b7280;

  --color-success: #059669;
  --color-success-foreground: #ffffff;
  --color-warning: #d97706;
  --color-warning-foreground: #ffffff;
  --color-error: #dc2626;
  --color-error-foreground: #ffffff;
  --color-info: #0ea5e9;
  --color-info-foreground: #ffffff;

  --color-background: #ffffff;
  --color-foreground: #111827;
  --color-surface: #f9fafb;
  --color-surface-secondary: #f3f4f6;
  --color-surface-tertiary: #e5e7eb;

  --color-text: #111827;
  --color-text-secondary: #6b7280;
  --color-text-muted: #9ca3af;
  --color-text-inverse: #ffffff;

  --color-border: #e5e7eb;
  --color-border-secondary: #d1d5db;
  --color-border-focus: #2563eb;

  --color-hover: #f1f5f9;
  --color-active: #e2e8f0;
  --color-focus: #dbeafe;
  --color-disabled: #f8fafc;
  --color-disabled-foreground: #cbd5e1;

  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #ffffff;
  --color-ring: #2563eb;
  --color-input: #ffffff;
  --color-input-foreground: #111827;
  --color-card: #ffffff;
  --color-card-foreground: #111827;
  --color-popover: #ffffff;
  --color-popover-foreground: #111827;

  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-secondary: rgba(0, 0, 0, 0.05);
  --color-overlay: rgba(0, 0, 0, 0.5);

  --color-chart-1: #2563eb;
  --color-chart-2: #059669;
  --color-chart-3: #d97706;
  --color-chart-4: #7c3aed;
  --color-chart-5: #dc2626;
}

/* Dark theme colors */
.dark {
  --color-primary: #3b82f6;
  --color-primary-foreground: #ffffff;
  --color-secondary: #6366f1;
  --color-secondary-foreground: #ffffff;
  --color-accent: #8b5cf6;
  --color-accent-foreground: #ffffff;
  --color-neutral: #9ca3af;

  --color-success: #10b981;
  --color-success-foreground: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-foreground: #111827;
  --color-error: #ef4444;
  --color-error-foreground: #ffffff;
  --color-info: #0ea5e9;
  --color-info-foreground: #ffffff;

  --color-background: #111827;
  --color-foreground: #f9fafb;
  --color-surface: #1f2937;
  --color-surface-secondary: #374151;
  --color-surface-tertiary: #4b5563;

  --color-text: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-text-muted: #9ca3af;
  --color-text-inverse: #111827;

  --color-border: #4b5563;
  --color-border-secondary: #6b7280;
  --color-border-focus: #3b82f6;

  --color-hover: #374151;
  --color-active: #4b5563;
  --color-focus: #1e3a8a;
  --color-disabled: #1f2937;
  --color-disabled-foreground: #6b7280;

  --color-muted: #374151;
  --color-muted-foreground: #9ca3af;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-ring: #3b82f6;
  --color-input: #1f2937;
  --color-input-foreground: #f9fafb;
  --color-card: #1f2937;
  --color-card-foreground: #f9fafb;
  --color-popover: #1f2937;
  --color-popover-foreground: #f9fafb;

  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-secondary: rgba(0, 0, 0, 0.2);
  --color-overlay: rgba(0, 0, 0, 0.7);

  --color-chart-1: #3b82f6;
  --color-chart-2: #10b981;
  --color-chart-3: #f59e0b;
  --color-chart-4: #8b5cf6;
  --color-chart-5: #ef4444;
}

/* Custom scrollbar utilities */
.scrollbar-hide {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-scrollbar: none;
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Smooth theme transitions */
* {
  transition-property:
    background-color, border-color, color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Dashboard animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.4s ease-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Hover effects for better interactivity */
.hover-lift {
  transition:
    transform 0.2s ease-out,
    box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Focus styles for accessibility */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}
