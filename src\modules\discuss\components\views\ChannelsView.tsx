import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { MessageList, MessageInput } from '../core';
import { MessageSearch, CallButtonGroup, CallInterface } from '../ui';
import { useMessages, useWebSocket, useTypingIndicator } from '../../hooks';
import { searchService } from '../../services';
import { mockUsers, getMockChannelById } from '../../../../mocks/data/discuss';
import type { Message, User, Call } from '../../types';
import type { SearchResult, SearchFilters } from '../ui/MessageSearch';

export interface ChannelsViewProps {
  className?: string;
  'data-testid'?: string;
}

export const ChannelsView: React.FC<ChannelsViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [currentChannelId] = useState('general'); // Default to general channel
  const [showSearch, setShowSearch] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [activeCall, setActiveCall] = useState<Call | null>(null);
  const currentUserId = '1'; // Mock current user

  // Use custom hooks for robust functionality
  const {
    messages,
    isLoading,
    error,
    hasMore,
    loadMore,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
  } = useMessages(currentUserId, {
    channelId: currentChannelId,
    autoLoad: true,
  });

  const {
    isConnected,
    joinChannel,
    leaveChannel,
  } = useWebSocket({
    userId: currentUserId,
    autoConnect: true,
  });

  const {
    typingUsers,
    startTyping,
    stopTyping,
  } = useTypingIndicator({
    channelId: currentChannelId,
    currentUserId,
  });

  // Join channel for real-time updates
  useEffect(() => {
    if (isConnected && currentChannelId) {
      joinChannel(currentChannelId);
    }

    return () => {
      if (currentChannelId) {
        leaveChannel(currentChannelId);
      }
    };
  }, [isConnected, currentChannelId, joinChannel, leaveChannel]);

  const handleSendMessage = async (content: string, attachments?: File[]) => {
    try {
      await sendMessage(content, attachments);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      // Check if user already reacted with this emoji
      const message = messages.find(m => m.id === messageId);
      const existingReaction = message?.reactions.find(r => r.emoji === emoji);
      const hasReacted = existingReaction?.userIds.includes(currentUserId);

      if (hasReacted) {
        await removeReaction(messageId, emoji);
      } else {
        await addReaction(messageId, emoji);
      }
    } catch (error) {
      console.error('Failed to handle reaction:', error);
    }
  };

  const handleTyping = (isTyping: boolean) => {
    if (isTyping) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  const handleSearch = async (query: string, filters: SearchFilters): Promise<SearchResult[]> => {
    try {
      const response = await searchService.searchMessages(query, filters);
      if (response.success) {
        return response.data.results;
      }
      return [];
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  };

  const handleSearchResultClick = (result: SearchResult) => {
    // Navigate to the message in the channel
    console.log('Navigate to message:', result.message.id, 'in channel:', result.channel.id);
    setShowSearch(false);
  };

  return (
    <div
      className={`flex-1 flex flex-col ${className}`}
      data-testid={testId}
    >
      {/* Channel Header */}
      <div
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-semibold" style={{ color: colors.text }}>
              📢 #general
            </h1>
            <span
              className="text-sm px-2 py-1 rounded-full"
              style={{
                backgroundColor: colors.backgroundSecondary,
                color: colors.textSecondary,
              }}
            >
              42 members
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSearch(!showSearch)}
              className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                showSearch ? 'bg-gray-100 dark:bg-gray-700' : ''
              }`}
              style={{ color: colors.textSecondary }}
              title="Search messages"
            >
              🔍
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="Channel info"
            >
              ℹ️
            </button>
            <CallButtonGroup
              channelId={currentChannelId}
              participantIds={getMockChannelById(currentChannelId)?.memberIds || []}
              currentUserId={currentUserId}
              onCallStarted={(callId) => {
                // In a real app, you'd fetch the call details
                const mockCall: Call = {
                  id: callId,
                  type: 'video',
                  channelId: currentChannelId,
                  participantIds: getMockChannelById(currentChannelId)?.memberIds || [],
                  startedBy: currentUserId,
                  startedAt: new Date(),
                  status: 'active',
                };
                setActiveCall(mockCall);
              }}
              variant="ghost"
              size="sm"
            />
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="More options"
            >
              ⋯
            </button>
          </div>
        </div>
        <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
          General discussion and announcements for the team
        </p>
      </div>

      {/* Search Bar */}
      {showSearch && (
        <div className="px-6 py-3 border-b" style={{ borderBottomColor: colors.border }}>
          <MessageSearch
            onSearch={handleSearch}
            onResultClick={handleSearchResultClick}
            placeholder={`Search in #${currentChannelId}...`}
          />
        </div>
      )}

      {/* Messages Area */}
      <MessageList
        messages={messages}
        users={mockUsers}
        currentUserId={currentUserId}
        channelId={currentChannelId}
        isLoading={isLoading}
        hasMore={hasMore}
        onLoadMore={loadMore}
        onReaction={handleReaction}
        onReply={(messageId) => console.log('Reply to:', messageId)}
        onEdit={editMessage}
        onDelete={deleteMessage}
        onPin={(messageId) => console.log('Pin:', messageId)}
      />

      {/* Typing Indicator */}
      {typingUsers.length > 0 && (
        <div className="px-6 py-2 border-t" style={{ borderTopColor: colors.border }}>
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-sm" style={{ color: colors.textSecondary }}>
              {typingUsers.length === 1
                ? `${typingUsers[0].userName} is typing...`
                : typingUsers.length === 2
                ? `${typingUsers[0].userName} and ${typingUsers[1].userName} are typing...`
                : `${typingUsers.length} people are typing...`
              }
            </span>
          </div>
        </div>
      )}

      {/* Connection Status */}
      {!isConnected && (
        <div className="px-6 py-2 bg-yellow-50 dark:bg-yellow-900/20 border-t" style={{ borderTopColor: colors.border }}>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm text-yellow-700 dark:text-yellow-300">
              Reconnecting...
            </span>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="px-6 py-2 bg-red-50 dark:bg-red-900/20 border-t" style={{ borderTopColor: colors.border }}>
          <div className="flex items-center justify-between">
            <span className="text-sm text-red-700 dark:text-red-300">
              {error}
            </span>
            <button
              onClick={() => window.location.reload()}
              className="text-sm text-red-700 dark:text-red-300 hover:underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <MessageInput
        placeholder="Type a message in #general..."
        channelId={currentChannelId}
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
      />

      {/* Active Call Interface */}
      {activeCall && (
        <CallInterface
          call={activeCall}
          currentUserId={currentUserId}
          onEndCall={() => setActiveCall(null)}
          onMinimize={() => {
            // In a real app, you might want to minimize to a floating window
            console.log('Call minimized');
          }}
        />
      )}
    </div>
  );
};
