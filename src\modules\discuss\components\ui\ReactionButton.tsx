import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Reaction } from '../../types';
import { EmojiPicker } from './EmojiPicker';

export interface ReactionButtonProps {
  reaction: Reaction;
  isUserReaction: boolean;
  onToggle: (emoji: string) => void;
  onRemove?: (emoji: string) => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const ReactionButton: React.FC<ReactionButtonProps> = ({
  reaction,
  isUserReaction,
  onToggle,
  onRemove,
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const handleClick = () => {
    if (disabled) return;
    
    if (isUserReaction && onRemove) {
      onRemove(reaction.emoji);
    } else {
      onToggle(reaction.emoji);
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${
        isUserReaction 
          ? 'ring-2 ring-blue-500 ring-opacity-50' 
          : 'hover:bg-gray-100 dark:hover:bg-gray-700'
      } ${className}`}
      style={{
        backgroundColor: isUserReaction 
          ? `${colors.primary}20` 
          : colors.backgroundSecondary,
        color: isUserReaction ? colors.primary : colors.text,
        borderColor: isUserReaction ? colors.primary : 'transparent',
      }}
      data-testid={testId}
    >
      <span className="text-sm">{reaction.emoji}</span>
      <span className="text-xs font-semibold">{reaction.count}</span>
    </button>
  );
};

export interface ReactionPickerProps {
  onReactionAdd: (emoji: string) => void;
  existingReactions?: Reaction[];
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const ReactionPicker: React.FC<ReactionPickerProps> = ({
  onReactionAdd,
  existingReactions = [],
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const quickReactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];
  
  const handleQuickReaction = (emoji: string) => {
    if (disabled) return;
    onReactionAdd(emoji);
  };

  const handleEmojiSelect = (emoji: string) => {
    if (disabled) return;
    onReactionAdd(emoji);
    setShowEmojiPicker(false);
  };

  const isReactionUsed = (emoji: string) => {
    return existingReactions.some(reaction => reaction.emoji === emoji);
  };

  return (
    <div className={`relative inline-flex items-center space-x-1 ${className}`} data-testid={testId}>
      {/* Quick Reactions */}
      {quickReactions.map((emoji) => (
        <button
          key={emoji}
          onClick={() => handleQuickReaction(emoji)}
          disabled={disabled}
          className={`p-1.5 rounded-full text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isReactionUsed(emoji) ? 'bg-gray-100 dark:bg-gray-700' : ''
          }`}
          title={`React with ${emoji}`}
        >
          {emoji}
        </button>
      ))}

      {/* More Reactions Button */}
      <div className="relative">
        <button
          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          disabled={disabled}
          className="p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ color: colors.textSecondary }}
          title="More reactions"
        >
          ➕
        </button>

        <EmojiPicker
          isOpen={showEmojiPicker}
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
          className="bottom-full right-0 mb-2"
        />
      </div>
    </div>
  );
};

export interface MessageReactionsProps {
  reactions: Reaction[];
  userReactions: string[];
  onReactionToggle: (emoji: string) => void;
  onReactionAdd: (emoji: string) => void;
  onReactionRemove: (emoji: string) => void;
  disabled?: boolean;
  showAddButton?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const MessageReactions: React.FC<MessageReactionsProps> = ({
  reactions,
  userReactions,
  onReactionToggle,
  onReactionAdd,
  onReactionRemove,
  disabled = false,
  showAddButton = true,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (reactions.length === 0 && !showAddButton) return null;

  return (
    <div className={`flex flex-wrap items-center gap-1 mt-2 ${className}`} data-testid={testId}>
      {/* Existing Reactions */}
      {reactions.map((reaction) => (
        <ReactionButton
          key={reaction.emoji}
          reaction={reaction}
          isUserReaction={userReactions.includes(reaction.emoji)}
          onToggle={onReactionToggle}
          onRemove={onReactionRemove}
          disabled={disabled}
        />
      ))}

      {/* Add Reaction Button */}
      {showAddButton && (
        <ReactionPicker
          onReactionAdd={onReactionAdd}
          existingReactions={reactions}
          disabled={disabled}
        />
      )}
    </div>
  );
};

export interface ReactionSummaryProps {
  reactions: Reaction[];
  maxVisible?: number;
  onReactionClick?: (emoji: string) => void;
  className?: string;
  'data-testid'?: string;
}

export const ReactionSummary: React.FC<ReactionSummaryProps> = ({
  reactions,
  maxVisible = 3,
  onReactionClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (reactions.length === 0) return null;

  const visibleReactions = reactions.slice(0, maxVisible);
  const hiddenCount = reactions.length - maxVisible;
  const totalCount = reactions.reduce((sum, reaction) => sum + reaction.count, 0);

  return (
    <div className={`flex items-center space-x-1 ${className}`} data-testid={testId}>
      {/* Visible Reactions */}
      {visibleReactions.map((reaction) => (
        <button
          key={reaction.emoji}
          onClick={() => onReactionClick?.(reaction.emoji)}
          className="inline-flex items-center space-x-0.5 text-xs hover:underline"
          style={{ color: colors.textSecondary }}
        >
          <span>{reaction.emoji}</span>
          <span>{reaction.count}</span>
        </button>
      ))}

      {/* Hidden Reactions Indicator */}
      {hiddenCount > 0 && (
        <span className="text-xs" style={{ color: colors.textSecondary }}>
          +{hiddenCount} more
        </span>
      )}

      {/* Total Count */}
      {reactions.length > 1 && (
        <span className="text-xs font-medium" style={{ color: colors.text }}>
          {totalCount} reactions
        </span>
      )}
    </div>
  );
};

export interface ReactionTooltipProps {
  reaction: Reaction;
  users: Array<{ id: string; name: string }>;
  isVisible: boolean;
  position?: { top: number; left: number };
  className?: string;
  'data-testid'?: string;
}

export const ReactionTooltip: React.FC<ReactionTooltipProps> = ({
  reaction,
  users,
  isVisible,
  position = { top: 0, left: 0 },
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (!isVisible) return null;

  const getTooltipText = () => {
    if (users.length === 0) return '';
    
    if (users.length === 1) {
      return `${users[0].name} reacted with ${reaction.emoji}`;
    } else if (users.length <= 3) {
      const names = users.map(user => user.name).join(', ');
      return `${names} reacted with ${reaction.emoji}`;
    } else {
      const firstTwo = users.slice(0, 2).map(user => user.name).join(', ');
      const remaining = users.length - 2;
      return `${firstTwo} and ${remaining} others reacted with ${reaction.emoji}`;
    }
  };

  return (
    <div
      className={`absolute z-50 px-2 py-1 text-xs rounded shadow-lg max-w-xs ${className}`}
      style={{
        top: position.top,
        left: position.left,
        backgroundColor: colors.surface,
        color: colors.text,
        border: `1px solid ${colors.border}`,
      }}
      data-testid={testId}
    >
      {getTooltipText()}
    </div>
  );
};
