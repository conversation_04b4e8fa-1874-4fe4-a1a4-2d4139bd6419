import type { Preview } from '@storybook/react';
import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import '../src/index.css';

// Theme provider wrapper for stories
const ThemeWrapper = ({ children }: { children: React.ReactNode }) => {
  // Apply dark theme class to body for Storybook
  React.useEffect(() => {
    document.body.classList.add('dark');
    return () => {
      document.body.classList.remove('dark');
    };
  }, []);

  return <div className="dark">{children}</div>;
};

const preview: Preview = {
  decorators: [
    Story => (
      <MemoryRouter>
        <ThemeWrapper>
          <Story />
        </ThemeWrapper>
      </MemoryRouter>
    ),
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },

    a11y: {
      // 'todo' - show a11y violations in the test UI only
      // 'error' - fail CI on a11y violations
      // 'off' - skip a11y checks entirely
      test: 'todo',
    },
  },
};

export default preview;
