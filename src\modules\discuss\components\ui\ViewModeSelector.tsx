import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export type ViewMode = 'comfortable' | 'compact' | 'cozy';

export interface ViewModeOption {
  mode: ViewMode;
  name: string;
  description: string;
  icon: string;
  preview?: string;
}

export interface ViewModeSelectorProps {
  currentMode: ViewMode;
  onModeChange: (mode: ViewMode) => void;
  options?: ViewModeOption[];
  showPreview?: boolean;
  className?: string;
  'data-testid'?: string;
}

const defaultOptions: ViewModeOption[] = [
  {
    mode: 'comfortable',
    name: 'Comfortable',
    description: 'Spacious layout with full avatars and timestamps',
    icon: '📏',
    preview: 'More space between messages, full user info displayed',
  },
  {
    mode: 'compact',
    name: 'Compact',
    description: 'Dense layout for viewing more messages',
    icon: '📋',
    preview: 'Minimal spacing, condensed message display',
  },
  {
    mode: 'cozy',
    name: 'Cozy',
    description: 'Balanced layout between comfortable and compact',
    icon: '🏠',
    preview: 'Moderate spacing with essential information',
  },
];

export const ViewModeSelector: React.FC<ViewModeSelectorProps> = ({
  currentMode,
  onModeChange,
  options = defaultOptions,
  showPreview = true,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div className={`space-y-4 ${className}`} data-testid={testId}>
      <div>
        <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
          Message Display
        </h3>
        <p className="text-sm" style={{ color: colors.textSecondary }}>
          Choose how messages are displayed in channels and conversations.
        </p>
      </div>

      <div className="space-y-3">
        {options.map((option) => (
          <div
            key={option.mode}
            className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
              currentMode === option.mode
                ? 'ring-2 ring-opacity-50'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}
            style={{
              borderColor: currentMode === option.mode ? colors.primary : colors.border,
              ringColor: currentMode === option.mode ? colors.primary : 'transparent',
              backgroundColor: currentMode === option.mode ? `${colors.primary}05` : 'transparent',
            }}
            onClick={() => onModeChange(option.mode)}
          >
            <div className="flex items-start space-x-3">
              {/* Icon */}
              <div className="text-2xl">{option.icon}</div>

              {/* Content */}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="font-medium" style={{ color: colors.text }}>
                    {option.name}
                  </h4>
                  {currentMode === option.mode && (
                    <div
                      className="text-xs px-2 py-1 rounded-full"
                      style={{
                        backgroundColor: colors.primary,
                        color: 'white',
                      }}
                    >
                      Current
                    </div>
                  )}
                </div>
                
                <p className="text-sm mb-2" style={{ color: colors.textSecondary }}>
                  {option.description}
                </p>

                {showPreview && option.preview && (
                  <div
                    className="text-xs p-2 rounded border"
                    style={{
                      backgroundColor: colors.backgroundSecondary,
                      borderColor: colors.border,
                      color: colors.textSecondary,
                    }}
                  >
                    {option.preview}
                  </div>
                )}
              </div>

              {/* Radio Button */}
              <div className="flex-shrink-0">
                <div
                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    currentMode === option.mode ? 'border-current' : ''
                  }`}
                  style={{
                    borderColor: currentMode === option.mode ? colors.primary : colors.border,
                  }}
                >
                  {currentMode === option.mode && (
                    <div
                      className="w-2.5 h-2.5 rounded-full"
                      style={{ backgroundColor: colors.primary }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Preview Section */}
      {showPreview && (
        <div
          className="border rounded-lg p-4"
          style={{
            borderColor: colors.border,
            backgroundColor: colors.backgroundSecondary,
          }}
        >
          <h4 className="font-medium mb-3" style={{ color: colors.text }}>
            Preview
          </h4>
          
          <div className="space-y-2">
            {/* Mock message preview based on current mode */}
            <div className={`flex items-start space-x-3 ${
              currentMode === 'compact' ? 'py-1' : currentMode === 'cozy' ? 'py-2' : 'py-3'
            }`}>
              {/* Avatar */}
              {(currentMode === 'comfortable' || currentMode === 'cozy') && (
                <div
                  className={`${currentMode === 'comfortable' ? 'w-10 h-10' : 'w-8 h-8'} rounded-full flex items-center justify-center text-white font-semibold`}
                  style={{ backgroundColor: colors.primary }}
                >
                  JD
                </div>
              )}

              {/* Message Content */}
              <div className="flex-1">
                {/* Header */}
                {currentMode !== 'compact' && (
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={`font-semibold ${currentMode === 'comfortable' ? 'text-sm' : 'text-xs'}`} style={{ color: colors.text }}>
                      John Doe
                    </span>
                    <span className="text-xs" style={{ color: colors.textSecondary }}>
                      2:30 PM
                    </span>
                  </div>
                )}

                {/* Message Text */}
                <div className={`${currentMode === 'compact' ? 'text-xs' : 'text-sm'} leading-relaxed`} style={{ color: colors.text }}>
                  {currentMode === 'compact' && (
                    <span className="font-semibold mr-2" style={{ color: colors.text }}>
                      John Doe:
                    </span>
                  )}
                  This is how messages will appear in {currentMode} mode.
                </div>
              </div>
            </div>

            {/* Second message */}
            <div className={`flex items-start space-x-3 ${
              currentMode === 'compact' ? 'py-1' : currentMode === 'cozy' ? 'py-2' : 'py-3'
            }`}>
              {(currentMode === 'comfortable' || currentMode === 'cozy') && (
                <div
                  className={`${currentMode === 'comfortable' ? 'w-10 h-10' : 'w-8 h-8'} rounded-full flex items-center justify-center text-white font-semibold`}
                  style={{ backgroundColor: '#10b981' }}
                >
                  JS
                </div>
              )}

              <div className="flex-1">
                {currentMode !== 'compact' && (
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={`font-semibold ${currentMode === 'comfortable' ? 'text-sm' : 'text-xs'}`} style={{ color: colors.text }}>
                      Jane Smith
                    </span>
                    <span className="text-xs" style={{ color: colors.textSecondary }}>
                      2:31 PM
                    </span>
                  </div>
                )}

                <div className={`${currentMode === 'compact' ? 'text-xs' : 'text-sm'} leading-relaxed`} style={{ color: colors.text }}>
                  {currentMode === 'compact' && (
                    <span className="font-semibold mr-2" style={{ color: colors.text }}>
                      Jane Smith:
                    </span>
                  )}
                  Perfect! The {currentMode} layout looks great for our team.
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export interface ViewModeToggleProps {
  currentMode: ViewMode;
  onModeChange: (mode: ViewMode) => void;
  modes?: ViewMode[];
  size?: 'small' | 'medium';
  className?: string;
  'data-testid'?: string;
}

export const ViewModeToggle: React.FC<ViewModeToggleProps> = ({
  currentMode,
  onModeChange,
  modes = ['comfortable', 'cozy', 'compact'],
  size = 'medium',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getModeIcon = (mode: ViewMode) => {
    switch (mode) {
      case 'comfortable': return '📏';
      case 'compact': return '📋';
      case 'cozy': return '🏠';
      default: return '📄';
    }
  };

  const buttonSize = size === 'small' ? 'p-1 text-xs' : 'p-2 text-sm';

  return (
    <div
      className={`inline-flex rounded-lg border ${className}`}
      style={{
        backgroundColor: colors.backgroundSecondary,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      {modes.map((mode, index) => (
        <button
          key={mode}
          onClick={() => onModeChange(mode)}
          className={`${buttonSize} transition-colors ${
            index === 0 ? 'rounded-l-lg' : index === modes.length - 1 ? 'rounded-r-lg' : ''
          } ${
            currentMode === mode
              ? 'text-white'
              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          style={{
            backgroundColor: currentMode === mode ? colors.primary : 'transparent',
            color: currentMode === mode ? 'white' : colors.text,
          }}
          title={mode.charAt(0).toUpperCase() + mode.slice(1)}
        >
          {getModeIcon(mode)}
        </button>
      ))}
    </div>
  );
};
