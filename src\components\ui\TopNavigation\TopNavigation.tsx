import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { BellIcon } from '../../icons';
import { cn } from '../../../utils/cn';

export interface TopNavigationProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    isOnline?: boolean;
  };
  notificationCount?: number;
  onNotificationClick?: () => void;
  onUserClick?: () => void;
  className?: string;
  'data-testid'?: string;
}

const TopNavigation: React.FC<TopNavigationProps> = ({
  user,
  notificationCount = 0,
  onNotificationClick,
  onUserClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const baseClasses = cn(
    'flex items-center justify-end space-x-4 p-4',
    className
  );

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={baseClasses} data-testid={testId}>
      {/* Online Status Indicator */}
      {user?.isOnline && (
        <div className="flex items-center space-x-2">
          <div
            className="w-2 h-2 rounded-full bg-green-500 animate-pulse"
            title="Online"
          />
        </div>
      )}

      {/* Notification Bell */}
      <button
        onClick={onNotificationClick}
        className="relative p-2 rounded-full hover:bg-white/10 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
        aria-label={`Notifications ${notificationCount > 0 ? `(${notificationCount})` : ''}`}
      >
        <BellIcon className="w-6 h-6" style={{ color: colors.text }} />

        {/* Notification Badge */}
        {notificationCount > 0 && (
          <div
            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center"
            style={{ fontSize: '10px' }}
          >
            {notificationCount > 99 ? '99+' : notificationCount}
          </div>
        )}
      </button>

      {/* User Avatar */}
      {user && (
        <button
          onClick={onUserClick}
          className="flex items-center space-x-2 p-2 rounded-full hover:bg-white/10 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
          aria-label={`User menu for ${user.name}`}
        >
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold"
            style={{
              backgroundColor: colors.primary,
              color: colors.primaryForeground,
            }}
          >
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              getInitials(user.name)
            )}
          </div>
        </button>
      )}
    </div>
  );
};

export default TopNavigation;
