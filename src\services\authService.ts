// Authentication service with mock data and API calls

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'moderator' | 'manager';
  avatar?: string;
  permissions: string[];
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  lastLogin?: string;
  preferences?: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    notifications: boolean;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expiresIn: number;
  refreshToken: string;
}

export class AuthError extends Error {
  public code: string;
  public details?: any;

  constructor({
    code,
    message,
    details,
  }: {
    code: string;
    message: string;
    details?: any;
  }) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
    this.details = details;
  }
}

// Mock user data for different user types
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '👨‍💼',
    permissions: [
      'read',
      'write',
      'delete',
      'admin',
      'manage_users',
      'manage_apps',
    ],
    status: 'active',
    createdAt: '2024-01-15T10:30:00Z',
    lastLogin: '2024-01-20T09:15:00Z',
    preferences: {
      theme: 'dark',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'manager',
    avatar: '👩‍💼',
    permissions: ['read', 'write', 'manage_team', 'view_reports'],
    status: 'active',
    createdAt: '2024-01-16T14:20:00Z',
    lastLogin: '2024-01-20T08:45:00Z',
    preferences: {
      theme: 'light',
      language: 'en',
      notifications: true,
    },
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'user',
    avatar: '👤',
    permissions: ['read', 'write'],
    status: 'active',
    createdAt: '2024-01-17T09:15:00Z',
    lastLogin: '2024-01-19T16:30:00Z',
    preferences: {
      theme: 'system',
      language: 'en',
      notifications: false,
    },
  },
  {
    id: '4',
    name: 'Alice Wilson',
    email: '<EMAIL>',
    role: 'moderator',
    avatar: '👮‍♀️',
    permissions: ['read', 'write', 'moderate', 'manage_content'],
    status: 'active',
    createdAt: '2024-01-18T11:45:00Z',
    lastLogin: '2024-01-20T07:20:00Z',
    preferences: {
      theme: 'dark',
      language: 'en',
      notifications: true,
    },
  },
];

// Mock login credentials for testing
export const mockCredentials = [
  { email: '<EMAIL>', password: 'admin123', userId: '1' },
  { email: '<EMAIL>', password: 'manager123', userId: '2' },
  { email: '<EMAIL>', password: 'user123', userId: '3' },
  { email: '<EMAIL>', password: 'moderator123', userId: '4' },
  // Quick login credentials for development
  { email: 'demo', password: 'demo', userId: '1' },
  { email: 'test', password: 'test', userId: '2' },
];

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock authentication service
export class AuthService {
  private static instance: AuthService;
  private currentUser: User | null = null;
  private token: string | null = null;

  private constructor() {
    // Load user from localStorage if available
    this.loadUserFromStorage();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Login method
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    await delay(1000); // Simulate network delay

    // Find matching credentials
    const authCred = mockCredentials.find(
      cred =>
        cred.email === credentials.email &&
        cred.password === credentials.password
    );

    if (!authCred) {
      throw new AuthError({
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password',
      });
    }

    // Find user data
    const user = mockUsers.find(u => u.id === authCred.userId);
    if (!user) {
      throw new AuthError({
        code: 'USER_NOT_FOUND',
        message: 'User not found',
      });
    }

    if (user.status !== 'active') {
      throw new AuthError({
        code: 'ACCOUNT_INACTIVE',
        message: 'Account is not active',
      });
    }

    // Update last login
    user.lastLogin = new Date().toISOString();

    // Generate mock tokens
    const token = `mock-jwt-token-${user.id}-${Date.now()}`;
    const refreshToken = `mock-refresh-token-${user.id}-${Date.now()}`;

    // Store user and token
    this.currentUser = user;
    this.token = token;
    this.saveUserToStorage(user, token);

    return {
      user,
      token,
      expiresIn: 3600, // 1 hour
      refreshToken,
    };
  }

  // Logout method
  async logout(): Promise<void> {
    await delay(300);
    this.currentUser = null;
    this.token = null;
    this.clearUserFromStorage();
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  // Get current token
  getToken(): string | null {
    return this.token;
  }

  // Refresh token
  async refreshToken(): Promise<string> {
    await delay(500);
    if (!this.currentUser) {
      throw new AuthError({
        code: 'NO_USER',
        message: 'No user to refresh token for',
      });
    }

    const newToken = `mock-jwt-token-${this.currentUser.id}-${Date.now()}`;
    this.token = newToken;
    this.saveUserToStorage(this.currentUser, newToken);
    return newToken;
  }

  // Update user preferences
  async updateUserPreferences(
    preferences: Partial<User['preferences']>
  ): Promise<User> {
    await delay(300);
    if (!this.currentUser) {
      throw new AuthError({
        code: 'NO_USER',
        message: 'No authenticated user',
      });
    }

    this.currentUser.preferences = {
      theme: 'dark',
      language: 'en',
      notifications: true,
      ...this.currentUser.preferences,
      ...preferences,
    };

    this.saveUserToStorage(this.currentUser, this.token!);
    return this.currentUser;
  }

  // Private methods for storage management
  private saveUserToStorage(user: User, token: string): void {
    try {
      localStorage.setItem('auth_user', JSON.stringify(user));
      localStorage.setItem('auth_token', token);
    } catch (error) {
      console.warn('Failed to save user to localStorage:', error);
    }
  }

  private loadUserFromStorage(): void {
    try {
      const userStr = localStorage.getItem('auth_user');
      const token = localStorage.getItem('auth_token');

      if (userStr && token) {
        this.currentUser = JSON.parse(userStr);
        this.token = token;
      }
    } catch (error) {
      console.warn('Failed to load user from localStorage:', error);
      this.clearUserFromStorage();
    }
  }

  private clearUserFromStorage(): void {
    try {
      localStorage.removeItem('auth_user');
      localStorage.removeItem('auth_token');
    } catch (error) {
      console.warn('Failed to clear user from localStorage:', error);
    }
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
