import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface ViewToggleProps {
  viewMode: 'grid' | 'kanban';
  onViewModeChange: (mode: 'grid' | 'kanban') => void;
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = '',
}) => {
  const { colors } = useThemeStore();

  return (
    <div className={cn("flex items-center gap-1 p-1 rounded-lg border", className)} 
         style={{ 
           backgroundColor: colors.surface, 
           borderColor: colors.border 
         }}>
      
      {/* Grid View Button */}
      <button
        onClick={() => onViewModeChange('grid')}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
          viewMode === 'grid'
            ? "bg-blue-600 text-white shadow-sm"
            : "hover:bg-gray-100 dark:hover:bg-gray-700"
        )}
        style={{
          color: viewMode === 'grid' ? '#ffffff' : colors.text,
        }}
        title="Grid View - Compact tiles with icons only"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
        Grid
      </button>

      {/* Kanban View Button */}
      <button
        onClick={() => onViewModeChange('kanban')}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
          viewMode === 'kanban'
            ? "bg-blue-600 text-white shadow-sm"
            : "hover:bg-gray-100 dark:hover:bg-gray-700"
        )}
        style={{
          color: viewMode === 'kanban' ? '#ffffff' : colors.text,
        }}
        title="Kanban View - Detailed tiles with descriptions"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0V17m0-10a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2h-2a2 2 0 01-2-2" />
        </svg>
        Kanban
      </button>
    </div>
  );
};

export default ViewToggle;