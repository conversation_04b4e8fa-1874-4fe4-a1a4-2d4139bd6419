import React, { useState, useEffect, useRef } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { User, Channel } from '../../types';
import { UserAvatar } from './UserPresence';

export interface MentionSuggestion {
  id: string;
  type: 'user' | 'channel' | 'everyone' | 'here';
  name: string;
  displayName: string;
  avatar?: string;
  description?: string;
  data?: User | Channel;
}

export interface MentionSuggestionsProps {
  suggestions: MentionSuggestion[];
  onSelect: (suggestion: MentionSuggestion) => void;
  onClose: () => void;
  isOpen: boolean;
  position?: { top: number; left: number };
  maxHeight?: number;
  className?: string;
  'data-testid'?: string;
}

export const MentionSuggestions: React.FC<MentionSuggestionsProps> = ({
  suggestions,
  onSelect,
  onClose,
  isOpen,
  position = { top: 0, left: 0 },
  maxHeight = 200,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const listRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Reset selection when suggestions change
  useEffect(() => {
    setSelectedIndex(0);
    itemRefs.current = itemRefs.current.slice(0, suggestions.length);
  }, [suggestions]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % suggestions.length);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
          break;
        case 'Enter':
          e.preventDefault();
          if (suggestions[selectedIndex]) {
            onSelect(suggestions[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, suggestions, onSelect, onClose]);

  // Scroll selected item into view
  useEffect(() => {
    const selectedItem = itemRefs.current[selectedIndex];
    if (selectedItem && listRef.current) {
      const container = listRef.current;
      const item = selectedItem;
      
      const containerTop = container.scrollTop;
      const containerBottom = containerTop + container.clientHeight;
      const itemTop = item.offsetTop;
      const itemBottom = itemTop + item.clientHeight;

      if (itemTop < containerTop) {
        container.scrollTop = itemTop;
      } else if (itemBottom > containerBottom) {
        container.scrollTop = itemBottom - container.clientHeight;
      }
    }
  }, [selectedIndex]);

  if (!isOpen || suggestions.length === 0) return null;

  const getMentionIcon = (type: MentionSuggestion['type']) => {
    switch (type) {
      case 'user':
        return '👤';
      case 'channel':
        return '#';
      case 'everyone':
        return '📢';
      case 'here':
        return '📍';
      default:
        return '💬';
    }
  };

  const getMentionColor = (type: MentionSuggestion['type']) => {
    switch (type) {
      case 'everyone':
      case 'here':
        return colors.error;
      case 'channel':
        return colors.primary;
      default:
        return colors.text;
    }
  };

  return (
    <div
      ref={listRef}
      className={`absolute z-50 border rounded-lg shadow-lg overflow-y-auto ${className}`}
      style={{
        top: position.top,
        left: position.left,
        maxHeight,
        backgroundColor: colors.background,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      <div className="py-2">
        {suggestions.map((suggestion, index) => (
          <button
            key={suggestion.id}
            ref={el => itemRefs.current[index] = el}
            onClick={() => onSelect(suggestion)}
            className={`w-full px-4 py-2 text-left flex items-center space-x-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
              index === selectedIndex ? 'bg-gray-100 dark:bg-gray-700' : ''
            }`}
          >
            {/* Icon/Avatar */}
            <div className="flex-shrink-0">
              {suggestion.type === 'user' && suggestion.data ? (
                <UserAvatar
                  user={suggestion.data as User}
                  size="small"
                  showStatus={false}
                />
              ) : (
                <div
                  className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold"
                  style={{
                    backgroundColor: `${getMentionColor(suggestion.type)}20`,
                    color: getMentionColor(suggestion.type),
                  }}
                >
                  {getMentionIcon(suggestion.type)}
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span
                  className="font-medium"
                  style={{ color: getMentionColor(suggestion.type) }}
                >
                  {suggestion.type === 'channel' ? '#' : '@'}{suggestion.displayName}
                </span>
                
                {suggestion.type === 'user' && (
                  <span className="text-xs px-1.5 py-0.5 rounded" style={{
                    backgroundColor: colors.backgroundSecondary,
                    color: colors.textSecondary,
                  }}>
                    {(suggestion.data as User)?.status || 'offline'}
                  </span>
                )}
              </div>
              
              {suggestion.description && (
                <p className="text-xs truncate" style={{ color: colors.textSecondary }}>
                  {suggestion.description}
                </p>
              )}
            </div>

            {/* Special mention warning */}
            {(suggestion.type === 'everyone' || suggestion.type === 'here') && (
              <div className="flex-shrink-0">
                <span className="text-xs px-2 py-1 rounded" style={{
                  backgroundColor: `${colors.error}20`,
                  color: colors.error,
                }}>
                  {suggestion.type === 'everyone' ? 'All members' : 'Online members'}
                </span>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export interface MentionInputProps {
  value: string;
  onChange: (value: string) => void;
  onMentionSelect: (mention: MentionSuggestion) => void;
  users: User[];
  channels: Channel[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const MentionInput: React.FC<MentionInputProps> = ({
  value,
  onChange,
  onMentionSelect,
  users,
  channels,
  placeholder = 'Type @ to mention someone...',
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [mentionQuery, setMentionQuery] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Parse mentions from input
  useEffect(() => {
    const input = inputRef.current;
    if (!input) return;

    const cursorPos = input.selectionStart || 0;
    const textBeforeCursor = value.substring(0, cursorPos);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const query = mentionMatch[1].toLowerCase();
      setMentionQuery(query);
      setCursorPosition(cursorPos);
      
      // Generate suggestions
      const userSuggestions: MentionSuggestion[] = users
        .filter(user => 
          user.name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query)
        )
        .slice(0, 8)
        .map(user => ({
          id: `user-${user.id}`,
          type: 'user',
          name: user.name,
          displayName: user.name,
          avatar: user.avatar,
          description: user.email,
          data: user,
        }));

      const channelSuggestions: MentionSuggestion[] = channels
        .filter(channel => 
          channel.name.toLowerCase().includes(query) ||
          (channel.description && channel.description.toLowerCase().includes(query))
        )
        .slice(0, 5)
        .map(channel => ({
          id: `channel-${channel.id}`,
          type: 'channel',
          name: channel.name,
          displayName: channel.name,
          description: channel.description,
          data: channel,
        }));

      const specialSuggestions: MentionSuggestion[] = [];
      
      if ('everyone'.includes(query)) {
        specialSuggestions.push({
          id: 'everyone',
          type: 'everyone',
          name: 'everyone',
          displayName: 'everyone',
          description: 'Notify all members in this channel',
        });
      }
      
      if ('here'.includes(query)) {
        specialSuggestions.push({
          id: 'here',
          type: 'here',
          name: 'here',
          displayName: 'here',
          description: 'Notify all online members in this channel',
        });
      }

      const allSuggestions = [...specialSuggestions, ...userSuggestions, ...channelSuggestions];
      setSuggestions(allSuggestions);
      setShowSuggestions(allSuggestions.length > 0);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
      setMentionQuery('');
    }
  }, [value, users, channels]);

  const handleSuggestionSelect = (suggestion: MentionSuggestion) => {
    const input = inputRef.current;
    if (!input) return;

    const mentionStart = cursorPosition - mentionQuery.length - 1; // -1 for @
    const beforeMention = value.substring(0, mentionStart);
    const afterMention = value.substring(cursorPosition);
    
    const mentionText = suggestion.type === 'channel' 
      ? `#${suggestion.name}` 
      : `@${suggestion.name}`;
    
    const newValue = beforeMention + mentionText + ' ' + afterMention;
    onChange(newValue);
    onMentionSelect(suggestion);
    
    setShowSuggestions(false);
    
    // Set cursor position after the mention
    setTimeout(() => {
      const newCursorPos = mentionStart + mentionText.length + 1;
      input.setSelectionRange(newCursorPos, newCursorPos);
      input.focus();
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Let MentionSuggestions handle navigation when open
    if (showSuggestions && ['ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(e.key)) {
      return;
    }
  };

  return (
    <div className={`relative ${className}`} data-testid={testId}>
      <textarea
        ref={inputRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none resize-none"
        style={{
          borderColor: colors.border,
          color: colors.text,
        }}
        rows={3}
      />
      
      <MentionSuggestions
        suggestions={suggestions}
        onSelect={handleSuggestionSelect}
        onClose={() => setShowSuggestions(false)}
        isOpen={showSuggestions}
        position={{ top: -200, left: 0 }}
      />
    </div>
  );
};
