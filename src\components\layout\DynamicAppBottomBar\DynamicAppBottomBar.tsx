import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import {
  Input,
  Pagination,
  ViewModeSwitcher,
} from '../../ui';
import CenteredSearchChipInput from '../../ui/CenteredSearchChipInput';
import type {
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from '../../ui/CenteredSearchChipInput/CenteredSearchChipInput';
import { cn } from '../../../utils/cn';
import {
  SearchIcon,
  ChevronDownIcon,
  PlusIcon,
  UploadIcon,
  XIcon,
} from '../../icons';

// Breadcrumb Types
export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
}

// Action Button Types
export interface ActionButton {
  label: string;
  onClick: () => void;
  isPrimary?: boolean;
  variant?: 'filled' | 'outline';
  confirm?: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
  };
}

// Type Definitions
export interface DynamicAppBottomBarProps {
  view: {
    title: string;
    breadcrumbs?: BreadcrumbItem[];
    actions: ActionButton[];
    search: {
      filterTags?: FilterTag[];
      filterItems?: FilterItem[];
      groupByItems?: GroupByItem[];
      favoriteItems?: FavoriteItem[];
      onSearch?: (query: string) => void;
      onTagRemove?: (tagId: string) => void;
      onFilterSelect?: (filterId: string) => void;
      onGroupBySelect?: (groupId: string) => void;
      onFavoriteSelect?: (favoriteId: string) => void;
      onFavoriteDelete?: (favoriteId: string) => void;
      onAddCustomFilter?: () => void;
      onAddCustomGroup?: () => void;
      onSaveCurrentSearch?: () => void;
      // Legacy support for existing basic search
      filters?: { id: any; label: string }[];
      onRemoveFilter?: (id: any) => void;
    };
    pagination: {
      currentRange: string;
      onNext: () => void;
      onPrev: () => void;
    };
    viewModes: { name: string; icon: React.ReactNode }[];
    activeViewMode: string;
    onViewModeChange?: (modeName: string) => void;
  };
  className?: string;
  'data-testid'?: string;
}

const DynamicAppBottomBar: React.FC<DynamicAppBottomBarProps> = ({
  view,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [isSearchDropdownOpen, setIsSearchDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    action?: ActionButton;
  }>({ isOpen: false });

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    view.search.onSearch?.(searchQuery);
  };

  const handleActionClick = (action: ActionButton) => {
    if (action.confirm) {
      setConfirmDialog({ isOpen: true, action });
    } else {
      action.onClick();
    }
  };

  const handleConfirmAction = () => {
    if (confirmDialog.action) {
      confirmDialog.action.onClick();
    }
    setConfirmDialog({ isOpen: false });
  };

  const handleCancelAction = () => {
    setConfirmDialog({ isOpen: false });
  };

  // Create breadcrumb items with title as the last item
  const breadcrumbItems: BreadcrumbItem[] = [
    ...(view.breadcrumbs || []),
    { label: view.title }
  ];

  // Compact Breadcrumb Component - matches the attachment design
  const Breadcrumb: React.FC<{ items: BreadcrumbItem[] }> = ({ items }) => (
    <nav className="flex items-center text-sm" aria-label="Breadcrumb">
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="mx-1" style={{ color: colors.mutedForeground }}>
              /
            </span>
          )}
          <div className="flex items-center">
            {item.href || item.onClick ? (
              <button
                onClick={item.onClick}
                className="hover:underline transition-colors duration-150 truncate max-w-[150px] px-1 py-0.5 rounded"
                style={{
                  color: index === items.length - 1 ? colors.text : colors.mutedForeground
                }}
                title={item.label}
              >
                {item.label}
              </button>
            ) : (
              <span
                className={cn(
                  "truncate px-1 py-0.5",
                  index === items.length - 1 ? "font-medium max-w-[200px]" : "max-w-[150px]"
                )}
                style={{
                  color: index === items.length - 1 ? colors.text : colors.mutedForeground
                }}
                title={item.label}
              >
                {item.label}
              </span>
            )}
          </div>
        </React.Fragment>
      ))}
    </nav>
  );

  // Compact Action Button Component
  const CompactActionButton: React.FC<{ action: ActionButton; index: number }> = ({ action, index }) => {
    const isOutline = action.variant === 'outline';
    const isPrimary = action.isPrimary && !isOutline;
    
    return (
      <button
        key={index}
        onClick={() => handleActionClick(action)}
        className={cn(
          "inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-150",
          "border focus:outline-none focus:ring-2 focus:ring-offset-1",
          isPrimary && "shadow-sm hover:shadow-md"
        )}
        style={{
          backgroundColor: isPrimary
            ? colors.primary
            : isOutline
              ? 'transparent'
              : colors.surface,
          color: isPrimary
            ? colors.primaryForeground
            : isOutline
              ? colors.primary
              : colors.text,
          borderColor: isPrimary
            ? colors.primary
            : isOutline
              ? colors.primary
              : colors.border,
          '--focus-ring-color': colors.primary + '33',
        } as React.CSSProperties & { '--focus-ring-color': string }}
        onMouseEnter={e => {
          if (isPrimary) {
            e.currentTarget.style.backgroundColor = colors.primary + 'E6';
          } else if (isOutline) {
            e.currentTarget.style.backgroundColor = colors.primary + '10';
          } else {
            e.currentTarget.style.backgroundColor = colors.hover;
          }
        }}
        onMouseLeave={e => {
          if (isPrimary) {
            e.currentTarget.style.backgroundColor = colors.primary;
          } else if (isOutline) {
            e.currentTarget.style.backgroundColor = 'transparent';
          } else {
            e.currentTarget.style.backgroundColor = colors.surface;
          }
        }}
        title={action.confirm ? `${action.label} (requires confirmation)` : action.label}
      >
        {action.label === 'New' && <PlusIcon className="w-4 h-4 mr-1.5" />}
        {action.label === 'Upload' && <UploadIcon className="w-4 h-4 mr-1.5" />}
        <span>{action.label}</span>
      </button>
    );
  };

  // Confirmation Dialog Component
  const ConfirmationDialog: React.FC = () => {
    if (!confirmDialog.isOpen || !confirmDialog.action?.confirm) return null;

    const { confirm } = confirmDialog.action;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={handleCancelAction}
        />
        
        {/* Dialog */}
        <div
          className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
            color: colors.text
          }}
        >
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            {confirm.title}
          </h3>
          <p className="text-sm mb-6" style={{ color: colors.textSecondary }}>
            {confirm.message}
          </p>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAction}
              className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-150"
              style={{
                backgroundColor: 'transparent',
                color: colors.text,
                borderColor: colors.border
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = colors.hover;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              {confirm.cancelText || 'Cancel'}
            </button>
            <button
              onClick={handleConfirmAction}
              className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-150"
              style={{
                backgroundColor: colors.primary,
                color: colors.primaryForeground,
                borderColor: colors.primary
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = colors.primary + 'E6';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = colors.primary;
              }}
            >
              {confirm.confirmText || 'Confirm'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Bottom Bar - Header-like Contextual Controls */}
      <div
        className={cn('border-t border-b shadow-sm', className)}
        style={{
          borderColor: colors.border,
          backgroundColor: colors.surface,
          boxShadow: `0 1px 3px ${colors.shadow}15`
        }}
        data-testid={testId}
      >
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left: Action Buttons and Breadcrumb */}
            <div className="flex items-center space-x-3">
              {/* Compact Action Buttons - Desktop */}
              <div className="hidden sm:flex items-center space-x-2">
                {view.actions.map((action, index) => (
                  <CompactActionButton key={index} action={action} index={index} />
                ))}
              </div>

              {/* Separator */}
              {view.actions.length > 0 && (
                <div
                  className="hidden sm:block w-px h-5 bg-current opacity-20"
                  style={{ color: colors.border }}
                />
              )}

              {/* Compact Dynamic Breadcrumb */}
              <div className="flex-1 min-w-0">
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>

            {/* Center: Search Bar with Dropdown */}
            <div className="flex-1 max-w-xl mx-8 hidden lg:block">
              <CenteredSearchChipInput
                placeholder="Search..."
                filterTags={view.search.filterTags}
                filterItems={view.search.filterItems}
                groupByItems={view.search.groupByItems}
                favoriteItems={view.search.favoriteItems}
                onSearch={view.search.onSearch}
                onTagRemove={view.search.onTagRemove}
                onFilterSelect={view.search.onFilterSelect}
                onGroupBySelect={view.search.onGroupBySelect}
                onFavoriteSelect={view.search.onFavoriteSelect}
                onFavoriteDelete={view.search.onFavoriteDelete}
                onAddCustomFilter={view.search.onAddCustomFilter}
                onAddCustomGroup={view.search.onAddCustomGroup}
                onSaveCurrentSearch={view.search.onSaveCurrentSearch}
                className="w-full"
              />
            </div>

            {/* Right: Controls */}
            <div className="flex items-center space-x-3">
              {/* Pagination */}
              <Pagination
                currentRange={view.pagination.currentRange}
                onNext={view.pagination.onNext}
                onPrev={view.pagination.onPrev}
                className="hidden md:flex"
              />

              {/* View Mode Switcher */}
              <ViewModeSwitcher
                viewModes={view.viewModes}
                activeViewMode={view.activeViewMode}
                onViewModeChange={view.onViewModeChange}
                className="hidden md:flex"
              />

              {/* Mobile Search Toggle */}
              <button
                className="lg:hidden p-2 rounded-md transition-colors duration-150"
                style={{
                  backgroundColor: 'transparent',
                  color: colors.mutedForeground,
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  e.currentTarget.style.color = colors.text;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.mutedForeground;
                }}
                onClick={() => setIsSearchExpanded(!isSearchExpanded)}
                aria-label="Toggle search"
              >
                <SearchIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Search Overlay */}
      {isSearchExpanded && (
        <div
          className="lg:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="p-4">
            <form onSubmit={handleSearchSubmit} className="space-y-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div style={{ color: colors.mutedForeground }}>
                    <SearchIcon className="w-5 h-5" />
                  </div>
                </div>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10 pr-12 py-3 w-full rounded-lg"
                  style={{
                    backgroundColor: colors.background,
                    border: 'none',
                    color: colors.text,
                  }}
                />
                {/* Mobile Chevron Dropdown Button */}
                <button
                  onClick={() => setIsSearchDropdownOpen(!isSearchDropdownOpen)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors duration-150"
                  style={{
                    color: colors.mutedForeground,
                    backgroundColor: 'transparent',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                    e.currentTarget.style.color = colors.text;
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = colors.mutedForeground;
                  }}
                  aria-label="Toggle search options"
                >
                  <ChevronDownIcon className="w-4 h-4" />
                </button>
              </div>

              {/* Mobile Filter Pills */}
              {view.search.filters && view.search.filters.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {view.search.filters.map(filter => (
                    <div
                      key={filter.id}
                      className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium"
                      style={{
                        backgroundColor: `${colors.primary}10`,
                        color: colors.text,
                      }}
                    >
                      <span>{filter.label}</span>
                      <button
                        onClick={() => view.search.onRemoveFilter?.(filter.id)}
                        className="p-0.5 rounded-full transition-colors duration-150"
                        style={{
                          backgroundColor: 'transparent',
                          color: colors.mutedForeground,
                        }}
                        onMouseEnter={e => {
                          e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                          e.currentTarget.style.color = colors.text;
                        }}
                        onMouseLeave={e => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = colors.mutedForeground;
                        }}
                        aria-label={`Remove ${filter.label} filter`}
                      >
                        <XIcon className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </form>
          </div>
        </div>
      )}

      {/* Floating Action Button - Mobile */}
      <div className="sm:hidden fixed bottom-6 right-6 z-50">
        {view.actions
          .filter(action => action.isPrimary)
          .map((action, index) => (
            <button
              key={index}
              onClick={() => handleActionClick(action)}
              className="w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
              style={{
                backgroundColor: colors.primary,
                color: colors.primaryForeground,
              }}
              aria-label={action.label}
            >
              {action.label === 'New' && <PlusIcon className="w-5 h-5" />}
              {action.label === 'Upload' && <UploadIcon className="w-5 h-5" />}
            </button>
          ))}
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </>
  );
};

export default DynamicAppBottomBar;