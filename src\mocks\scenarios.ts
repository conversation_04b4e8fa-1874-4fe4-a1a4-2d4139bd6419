// MSW scenarios for different testing and development needs
import { getCurrentConfig, updateMockConfig } from './config';
import { handlers } from './handlers';

export interface Scenario {
  name: string;
  description: string;
  config: {
    enabled: boolean;
    baseDelay: number;
    maxDelay: number;
    errorRate: number;
    enableLogging: boolean;
    enableErrorSimulation: boolean;
    enableNetworkDelay: boolean;
  };
  setup?: () => void;
  teardown?: () => void;
}

// Predefined scenarios
export const scenarios: Record<string, Scenario> = {
  // Default development scenario
  development: {
    name: 'Development',
    description:
      'Standard development environment with moderate delays and some errors',
    config: {
      enabled: true,
      baseDelay: 200,
      maxDelay: 800,
      errorRate: 0.02,
      enableLogging: true,
      enableErrorSimulation: true,
      enableNetworkDelay: true,
    },
  },

  // Fast testing scenario
  fastTesting: {
    name: 'Fast Testing',
    description: 'Minimal delays for fast test execution',
    config: {
      enabled: true,
      baseDelay: 0,
      maxDelay: 50,
      errorRate: 0,
      enableLogging: false,
      enableErrorSimulation: false,
      enableNetworkDelay: false,
    },
  },

  // Error testing scenario
  errorTesting: {
    name: 'Error Testing',
    description: 'High error rates for testing error handling',
    config: {
      enabled: true,
      baseDelay: 100,
      maxDelay: 300,
      errorRate: 0.3,
      enableLogging: true,
      enableErrorSimulation: true,
      enableNetworkDelay: true,
    },
  },

  // Slow network scenario
  slowNetwork: {
    name: 'Slow Network',
    description: 'Simulate slow network conditions',
    config: {
      enabled: true,
      baseDelay: 2000,
      maxDelay: 8000,
      errorRate: 0.1,
      enableLogging: true,
      enableErrorSimulation: true,
      enableNetworkDelay: true,
    },
  },

  // Offline scenario
  offline: {
    name: 'Offline',
    description: 'Simulate offline conditions with network errors',
    config: {
      enabled: true,
      baseDelay: 1000,
      maxDelay: 3000,
      errorRate: 0.8,
      enableLogging: true,
      enableErrorSimulation: true,
      enableNetworkDelay: true,
    },
  },

  // Production-like scenario
  production: {
    name: 'Production-like',
    description: 'Fast responses with minimal errors, similar to production',
    config: {
      enabled: true,
      baseDelay: 50,
      maxDelay: 200,
      errorRate: 0.001,
      enableLogging: false,
      enableErrorSimulation: false,
      enableNetworkDelay: true,
    },
  },

  // Demo scenario
  demo: {
    name: 'Demo',
    description: 'Optimized for demonstrations with predictable behavior',
    config: {
      enabled: true,
      baseDelay: 300,
      maxDelay: 600,
      errorRate: 0,
      enableLogging: false,
      enableErrorSimulation: false,
      enableNetworkDelay: true,
    },
  },

  // Load testing scenario
  loadTesting: {
    name: 'Load Testing',
    description: 'Variable delays and errors to simulate load conditions',
    config: {
      enabled: true,
      baseDelay: 100,
      maxDelay: 2000,
      errorRate: 0.05,
      enableLogging: true,
      enableErrorSimulation: true,
      enableNetworkDelay: true,
    },
  },
};

// Current active scenario
let currentScenario: string | null = null;

// Apply a scenario
export const applyScenario = (scenarioName: string): boolean => {
  const scenario = scenarios[scenarioName];
  if (!scenario) {
    console.warn(`Scenario '${scenarioName}' not found`);
    return false;
  }

  // Teardown current scenario if any
  if (currentScenario && scenarios[currentScenario]?.teardown) {
    scenarios[currentScenario].teardown!();
  }

  // Apply new scenario configuration
  updateMockConfig(scenario.config);

  // Setup new scenario
  if (scenario.setup) {
    scenario.setup();
  }

  currentScenario = scenarioName;
  console.log(`Applied MSW scenario: ${scenario.name}`);
  return true;
};

// Get current scenario
export const getCurrentScenario = (): string | null => {
  return currentScenario;
};

// Get available scenarios
export const getAvailableScenarios = (): Record<string, Scenario> => {
  return scenarios;
};

// Reset to default scenario
export const resetToDefaultScenario = (): void => {
  const environment = import.meta.env.MODE || 'development';
  const defaultScenario = environment === 'test' ? 'fastTesting' : environment;

  if (scenarios[defaultScenario]) {
    applyScenario(defaultScenario);
  } else {
    applyScenario('development');
  }
};

// Auto-apply scenario based on environment
export const autoApplyScenario = (): void => {
  const environment = import.meta.env.MODE || 'development';
  const urlParams = new URLSearchParams(window.location.search);
  const scenarioParam = urlParams.get('mswScenario');

  // Priority: URL parameter > environment-based
  if (scenarioParam && scenarios[scenarioParam]) {
    applyScenario(scenarioParam);
  } else {
    resetToDefaultScenario();
  }
};

// Scenario management utilities
export const createCustomScenario = (
  name: string,
  description: string,
  config: Scenario['config'],
  setup?: () => void,
  teardown?: () => void
): void => {
  scenarios[name] = {
    name,
    description,
    config,
    setup,
    teardown,
  };
};

export const removeScenario = (name: string): boolean => {
  if (scenarios[name]) {
    if (currentScenario === name) {
      resetToDefaultScenario();
    }
    delete scenarios[name];
    return true;
  }
  return false;
};

// Development helpers
export const enableDevMode = (): void => {
  applyScenario('development');

  // Add global helpers for browser console
  if (typeof window !== 'undefined') {
    (window as any).mswScenarios = {
      apply: applyScenario,
      current: getCurrentScenario,
      available: getAvailableScenarios,
      reset: resetToDefaultScenario,
      config: getCurrentConfig,
    };

    console.log('MSW scenarios available in console as window.mswScenarios');
  }
};

// Initialize scenarios
export const initializeScenarios = (): void => {
  if (import.meta.env.DEV) {
    enableDevMode();
  }

  autoApplyScenario();
};
