import React from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import { cn } from '../../../utils/cn';

export interface CardField {
  key: string;
  label?: string;
  render?: (value: any, item: any) => React.ReactNode;
  className?: string;
  showLabel?: boolean;
}

export interface CardViewProps extends BaseViewProps {
  fields: CardField[];
  columns?: number;
  gap?: number;
  minCardWidth?: number;
  maxCardWidth?: number;
  showHeader?: boolean;
  headerField?: string;
  imageField?: string;
  selectable?: boolean;
  selectedItems?: string[];
  itemKey?: string | ((item: any) => string);
  onItemSelect?: (item: any, selected: boolean) => void;
  renderHeader?: (item: any, index: number) => React.ReactNode;
  renderFooter?: (item: any, index: number) => React.ReactNode;
  renderActions?: (item: any, index: number) => React.ReactNode;
  cardClassName?: string;
}

export const CardView: React.FC<CardViewProps> = ({
  data,
  fields,
  columns,
  gap = 4,
  minCardWidth = 280,
  maxCardWidth,
  showHeader = true,
  headerField = 'title',
  imageField,
  selectable = false,
  selectedItems = [],
  itemKey = 'id',
  onItemSelect,
  renderHeader,
  renderFooter,
  renderActions,
  cardClassName,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  const getItemKey = (item: any, index: number): string => {
    if (typeof itemKey === 'function') {
      return itemKey(item);
    }
    return item[itemKey] || index.toString();
  };

  const isSelected = (item: any): boolean => {
    const key = getItemKey(item, 0);
    return selectedItems.includes(key);
  };

  const handleItemSelect = (
    item: any,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (onItemSelect) {
      onItemSelect(item, event.target.checked);
    }
  };

  const getGridStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {
      display: 'grid',
      gap: `${gap * 0.25}rem`,
    };

    if (columns) {
      style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    } else {
      const minWidth = `${minCardWidth}px`;
      const maxWidth = maxCardWidth ? `${maxCardWidth}px` : '1fr';
      style.gridTemplateColumns = `repeat(auto-fill, minmax(${minWidth}, ${maxWidth}))`;
    }

    return style;
  };

  const renderField = (field: CardField, item: any) => {
    const value = item[field.key];

    if (field.render) {
      return field.render(value, item);
    }

    return value?.toString() || '';
  };

  const renderCardHeader = (item: any, index: number) => {
    if (renderHeader) {
      return renderHeader(item, index);
    }

    if (!showHeader) return null;

    const headerValue = item[headerField];
    if (!headerValue) return null;

    return (
      <div
        className="px-4 py-3 border-b"
        style={{ borderBottomColor: colors.border }}
      >
        <h3
          className="font-semibold text-lg truncate"
          style={{ color: colors.text }}
        >
          {headerValue}
        </h3>
      </div>
    );
  };

  const renderCardImage = (item: any) => {
    if (!imageField || !item[imageField]) return null;

    return (
      <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
        <img
          src={item[imageField]}
          alt={item[headerField] || 'Card image'}
          className="w-full h-full object-cover"
        />
      </div>
    );
  };

  const renderCardBody = (item: any) => (
    <div className="px-4 py-3 flex-1">
      <div className="space-y-2">
        {fields.map(field => {
          const value = renderField(field, item);
          if (!value) return null;

          return (
            <div key={field.key} className={cn('text-sm', field.className)}>
              {field.showLabel !== false && field.label && (
                <span
                  className="font-medium mr-2"
                  style={{ color: colors.textSecondary }}
                >
                  {field.label}:
                </span>
              )}
              <span style={{ color: colors.text }}>{value}</span>
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderCardFooter = (item: any, index: number) => {
    if (!renderFooter) return null;

    return (
      <div
        className="px-4 py-3 border-t"
        style={{ borderTopColor: colors.border }}
      >
        {renderFooter(item, index)}
      </div>
    );
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div style={getGridStyle()}>
        {data.map((item, index) => {
          const key = getItemKey(item, index);
          const selected = isSelected(item);

          return (
            <div
              key={key}
              className={cn(
                'relative rounded-lg border transition-all duration-200 flex flex-col',
                'hover:shadow-md hover:scale-[1.01]',
                selected && 'ring-2 ring-blue-500 ring-opacity-50',
                baseProps.onItemClick && 'cursor-pointer',
                cardClassName
              )}
              style={{
                backgroundColor: colors.surface,
                borderColor: selected ? colors.primary : colors.border,
              }}
              onClick={() => baseProps.onItemClick?.(item, index)}
            >
              {/* Selection checkbox */}
              {selectable && (
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    className="rounded"
                    checked={selected}
                    onChange={e => handleItemSelect(item, e)}
                    onClick={e => e.stopPropagation()}
                  />
                </div>
              )}

              {/* Card actions */}
              {renderActions && (
                <div className="absolute top-2 right-2 z-10">
                  <div onClick={e => e.stopPropagation()}>
                    {renderActions(item, index)}
                  </div>
                </div>
              )}

              {/* Card image */}
              {renderCardImage(item)}

              {/* Card header */}
              {renderCardHeader(item, index)}

              {/* Card body */}
              {renderCardBody(item)}

              {/* Card footer */}
              {renderCardFooter(item, index)}
            </div>
          );
        })}
      </div>
    </BaseView>
  );
};

export default CardView;
