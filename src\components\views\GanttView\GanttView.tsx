import React, { useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { GanttViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  progress?: number;
  dependencies?: string[];
  resource?: string;
  parent?: string;
  level: number;
  data: any;
}

export interface GanttViewProps extends BaseViewProps {
  config: GanttViewConfig;
  onTaskClick?: (task: GanttTask) => void;
  onTaskResize?: (taskId: string, newStartDate: Date, newEndDate: Date) => void;
  onTaskMove?: (taskId: string, newStartDate: Date) => void;
  renderTask?: (task: GanttTask) => React.ReactNode;
  renderTaskTooltip?: (task: GanttTask) => React.ReactNode;
}

export const GanttView: React.FC<GanttViewProps> = ({
  data,
  config,
  onTaskClick,
  onTaskResize,
  onTaskMove,
  renderTask,
  renderTaskTooltip,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  // Convert data to Gantt tasks
  const tasks = useMemo((): GanttTask[] => {
    const taskMap = new Map<string, GanttTask>();

    // First pass: create all tasks
    data.forEach((item, index) => {
      const task: GanttTask = {
        id: item.id || index.toString(),
        name: item[config.taskNameField] || 'Untitled Task',
        startDate: new Date(item[config.startDateField]),
        endDate: new Date(item[config.endDateField]),
        progress: config.progressField
          ? parseFloat(item[config.progressField]) || 0
          : 0,
        dependencies: config.dependenciesField
          ? item[config.dependenciesField]
          : [],
        resource: config.resourceField ? item[config.resourceField] : undefined,
        parent: config.parentField ? item[config.parentField] : undefined,
        level: 0,
        data: item,
      };
      taskMap.set(task.id, task);
    });

    // Second pass: calculate hierarchy levels
    const calculateLevel = (
      taskId: string,
      visited = new Set<string>()
    ): number => {
      if (visited.has(taskId)) return 0; // Prevent circular references
      visited.add(taskId);

      const task = taskMap.get(taskId);
      if (!task || !task.parent) return 0;

      return 1 + calculateLevel(task.parent, visited);
    };

    Array.from(taskMap.values()).forEach(task => {
      task.level = calculateLevel(task.id);
    });

    return Array.from(taskMap.values()).sort((a, b) => {
      // Sort by level first, then by start date
      if (a.level !== b.level) return a.level - b.level;
      return a.startDate.getTime() - b.startDate.getTime();
    });
  }, [data, config]);

  // Calculate timeline bounds
  const timelineBounds = useMemo(() => {
    if (tasks.length === 0) return null;

    const startDates = tasks.map(t => t.startDate.getTime());
    const endDates = tasks.map(t => t.endDate.getTime());

    const minDate = new Date(Math.min(...startDates));
    const maxDate = new Date(Math.max(...endDates));

    // Add some padding
    const padding = (maxDate.getTime() - minDate.getTime()) * 0.1;
    minDate.setTime(minDate.getTime() - padding);
    maxDate.setTime(maxDate.getTime() + padding);

    return { start: minDate, end: maxDate };
  }, [tasks]);

  // Generate time scale
  const timeScale = useMemo(() => {
    if (!timelineBounds) return [];

    const { start, end } = timelineBounds;
    const totalMs = end.getTime() - start.getTime();
    const scale = config.timeScale || 'day';
    const intervals: Date[] = [];

    const current = new Date(start);

    switch (scale) {
      case 'day':
        current.setHours(0, 0, 0, 0);
        while (current <= end) {
          intervals.push(new Date(current));
          current.setDate(current.getDate() + 1);
        }
        break;
      case 'week':
        current.setDate(current.getDate() - current.getDay());
        current.setHours(0, 0, 0, 0);
        while (current <= end) {
          intervals.push(new Date(current));
          current.setDate(current.getDate() + 7);
        }
        break;
      case 'month':
        current.setDate(1);
        current.setHours(0, 0, 0, 0);
        while (current <= end) {
          intervals.push(new Date(current));
          current.setMonth(current.getMonth() + 1);
        }
        break;
    }

    return intervals;
  }, [timelineBounds, config.timeScale]);

  // Convert date to pixel position
  const dateToPixel = (date: Date, chartWidth: number) => {
    if (!timelineBounds) return 0;

    const totalMs =
      timelineBounds.end.getTime() - timelineBounds.start.getTime();
    const dateMs = date.getTime() - timelineBounds.start.getTime();

    return (dateMs / totalMs) * chartWidth;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year:
        timelineBounds &&
        timelineBounds.end.getFullYear() !== timelineBounds.start.getFullYear()
          ? 'numeric'
          : undefined,
    });
  };

  const renderGanttChart = () => {
    const chartWidth = 800;
    const rowHeight = 40;
    const headerHeight = 60;
    const taskNameWidth = 200;

    return (
      <div
        className="overflow-auto border rounded-lg"
        style={{ borderColor: colors.border }}
      >
        <div className="flex">
          {/* Task names column */}
          <div
            className="flex-shrink-0 border-r"
            style={{ width: taskNameWidth, borderRightColor: colors.border }}
          >
            {/* Header */}
            <div
              className="p-3 border-b font-semibold"
              style={{
                height: headerHeight,
                borderBottomColor: colors.border,
                backgroundColor: colors.surface,
                color: colors.text,
              }}
            >
              Task Name
            </div>

            {/* Task rows */}
            {tasks.map((task, index) => (
              <div
                key={task.id}
                className={cn(
                  'p-3 border-b flex items-center cursor-pointer hover:bg-gray-50',
                  index % 2 === 1 && 'bg-opacity-25'
                )}
                style={{
                  height: rowHeight,
                  borderBottomColor: colors.border,
                  backgroundColor:
                    index % 2 === 1 ? colors.muted : 'transparent',
                  paddingLeft: `${task.level * 20 + 12}px`,
                }}
                onClick={() => onTaskClick?.(task)}
              >
                <div className="truncate" style={{ color: colors.text }}>
                  {task.name}
                </div>
              </div>
            ))}
          </div>

          {/* Timeline chart */}
          <div className="flex-1">
            {/* Timeline header */}
            <div
              className="border-b"
              style={{
                height: headerHeight,
                borderBottomColor: colors.border,
                backgroundColor: colors.surface,
              }}
            >
              <div className="flex h-full">
                {timeScale.map((date, index) => (
                  <div
                    key={index}
                    className="flex-1 p-2 border-r text-center text-sm"
                    style={{
                      borderRightColor: colors.border,
                      color: colors.textSecondary,
                      minWidth: '80px',
                    }}
                  >
                    {formatDate(date)}
                  </div>
                ))}
              </div>
            </div>

            {/* Task bars */}
            <div className="relative">
              {tasks.map((task, index) => {
                const startX = dateToPixel(task.startDate, chartWidth);
                const endX = dateToPixel(task.endDate, chartWidth);
                const width = Math.max(endX - startX, 4);
                const y = index * rowHeight;

                return (
                  <div key={task.id}>
                    {/* Task row background */}
                    <div
                      className={cn(
                        'border-b',
                        index % 2 === 1 && 'bg-opacity-25'
                      )}
                      style={{
                        height: rowHeight,
                        borderBottomColor: colors.border,
                        backgroundColor:
                          index % 2 === 1 ? colors.muted : 'transparent',
                      }}
                    />

                    {/* Task bar */}
                    <div
                      className="absolute rounded cursor-pointer hover:opacity-80 transition-opacity"
                      style={{
                        left: startX,
                        top: y + 8,
                        width: width,
                        height: rowHeight - 16,
                        backgroundColor: colors.primary,
                      }}
                      onClick={() => onTaskClick?.(task)}
                      title={`${task.name}\n${formatDate(task.startDate)} - ${formatDate(task.endDate)}`}
                    >
                      {/* Progress bar */}
                      {task.progress !== undefined && task.progress > 0 && (
                        <div
                          className="h-full rounded bg-green-500 opacity-60"
                          style={{ width: `${task.progress}%` }}
                        />
                      )}

                      {/* Task label */}
                      <div className="absolute inset-0 flex items-center px-2">
                        <span className="text-white text-xs font-medium truncate">
                          {task.name}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Today line */}
              {timelineBounds && (
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-red-500 opacity-60 pointer-events-none"
                  style={{ left: dateToPixel(new Date(), chartWidth) }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-6">
        {tasks.length > 0 ? (
          renderGanttChart()
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📊</div>
            <h3
              className="text-lg font-semibold mb-2"
              style={{ color: colors.text }}
            >
              No Tasks Available
            </h3>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              No tasks with valid start and end dates found for the Gantt chart.
            </p>
          </div>
        )}
      </div>
    </BaseView>
  );
};

export default GanttView;
