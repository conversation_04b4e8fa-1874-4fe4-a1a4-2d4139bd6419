// Workflow engine for complex multi-step automation
import type { 
  Workflow, 
  WorkflowStep, 
  WorkflowTrigger,
  WorkflowVariable,
  WorkflowCondition,
  AutomationContext
} from '../types';
import { messageService } from './messageService';
import { websocketService } from './websocketService';

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
  currentStep?: string;
  variables: Record<string, any>;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  executionLog: WorkflowLogEntry[];
}

export interface WorkflowLogEntry {
  timestamp: Date;
  stepId?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: any;
}

export interface WorkflowStepResult {
  success: boolean;
  nextSteps: string[];
  variables?: Record<string, any>;
  error?: string;
  shouldPause?: boolean;
  shouldStop?: boolean;
}

export class WorkflowEngine {
  private workflows: Map<string, Workflow> = new Map();
  private executions: Map<string, WorkflowExecution> = new Map();
  private stepHandlers: Map<string, WorkflowStepHandler> = new Map();
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeEngine();
    this.registerBuiltInHandlers();
    this.startExecutionProcessor();
  }

  private initializeEngine(): void {
    // Listen for events that might trigger workflows
    websocketService.on('message_created', this.handleEvent.bind(this, 'message_created'));
    websocketService.on('user_joined', this.handleEvent.bind(this, 'user_joined'));
    websocketService.on('user_left', this.handleEvent.bind(this, 'user_left'));
  }

  // Register a workflow
  registerWorkflow(workflow: Workflow): void {
    this.workflows.set(workflow.id, workflow);
  }

  // Start workflow execution
  async startWorkflow(
    workflowId: string, 
    context: AutomationContext, 
    initialVariables?: Record<string, any>
  ): Promise<WorkflowExecution> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    if (workflow.status !== 'active') {
      throw new Error(`Workflow is not active: ${workflowId}`);
    }

    const executionId = this.generateExecutionId();
    const execution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: 'running',
      variables: { ...initialVariables, ...this.initializeWorkflowVariables(workflow) },
      startedAt: new Date(),
      executionLog: []
    };

    this.executions.set(executionId, execution);
    this.logExecution(execution, 'info', `Workflow started: ${workflow.name}`);

    // Find entry points (steps with no dependencies)
    const entrySteps = workflow.steps.filter(step => 
      !workflow.steps.some(s => s.nextSteps.includes(step.id))
    );

    if (entrySteps.length === 0) {
      this.logExecution(execution, 'error', 'No entry points found in workflow');
      execution.status = 'failed';
      execution.error = 'No entry points found';
      execution.completedAt = new Date();
      return execution;
    }

    // Start with the first entry step
    execution.currentStep = entrySteps[0].id;
    this.executions.set(executionId, execution);

    return execution;
  }

  // Pause workflow execution
  async pauseWorkflow(executionId: string): Promise<boolean> {
    const execution = this.executions.get(executionId);
    if (!execution || execution.status !== 'running') {
      return false;
    }

    execution.status = 'paused';
    this.logExecution(execution, 'info', 'Workflow paused');
    this.executions.set(executionId, execution);
    return true;
  }

  // Resume workflow execution
  async resumeWorkflow(executionId: string): Promise<boolean> {
    const execution = this.executions.get(executionId);
    if (!execution || execution.status !== 'paused') {
      return false;
    }

    execution.status = 'running';
    this.logExecution(execution, 'info', 'Workflow resumed');
    this.executions.set(executionId, execution);
    return true;
  }

  // Cancel workflow execution
  async cancelWorkflow(executionId: string): Promise<boolean> {
    const execution = this.executions.get(executionId);
    if (!execution || ['completed', 'failed', 'cancelled'].includes(execution.status)) {
      return false;
    }

    execution.status = 'cancelled';
    execution.completedAt = new Date();
    this.logExecution(execution, 'info', 'Workflow cancelled');
    this.executions.set(executionId, execution);
    return true;
  }

  // Get workflow execution
  getExecution(executionId: string): WorkflowExecution | undefined {
    return this.executions.get(executionId);
  }

  // Get all executions for a workflow
  getWorkflowExecutions(workflowId: string): WorkflowExecution[] {
    return Array.from(this.executions.values())
      .filter(e => e.workflowId === workflowId)
      .sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
  }

  // Process workflow executions
  private startExecutionProcessor(): void {
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing) {
        await this.processExecutions();
      }
    }, 1000); // Process every second
  }

  private async processExecutions(): Promise<void> {
    this.isProcessing = true;

    try {
      const runningExecutions = Array.from(this.executions.values())
        .filter(e => e.status === 'running' && e.currentStep);

      for (const execution of runningExecutions) {
        await this.processExecution(execution);
      }
    } catch (error) {
      console.error('Error processing workflow executions:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processExecution(execution: WorkflowExecution): Promise<void> {
    const workflow = this.workflows.get(execution.workflowId);
    if (!workflow) {
      execution.status = 'failed';
      execution.error = 'Workflow not found';
      execution.completedAt = new Date();
      return;
    }

    const currentStep = workflow.steps.find(s => s.id === execution.currentStep);
    if (!currentStep) {
      execution.status = 'completed';
      execution.completedAt = new Date();
      this.logExecution(execution, 'info', 'Workflow completed - no more steps');
      return;
    }

    try {
      const result = await this.executeStep(currentStep, execution, workflow);
      
      if (result.shouldStop) {
        execution.status = 'completed';
        execution.completedAt = new Date();
        this.logExecution(execution, 'info', 'Workflow stopped by step');
        return;
      }

      if (result.shouldPause) {
        execution.status = 'paused';
        this.logExecution(execution, 'info', 'Workflow paused by step');
        return;
      }

      if (!result.success) {
        execution.status = 'failed';
        execution.error = result.error || 'Step execution failed';
        execution.completedAt = new Date();
        this.logExecution(execution, 'error', `Step failed: ${result.error}`);
        return;
      }

      // Update variables
      if (result.variables) {
        execution.variables = { ...execution.variables, ...result.variables };
      }

      // Move to next step(s)
      if (result.nextSteps.length === 0) {
        execution.status = 'completed';
        execution.completedAt = new Date();
        execution.currentStep = undefined;
        this.logExecution(execution, 'info', 'Workflow completed');
      } else if (result.nextSteps.length === 1) {
        execution.currentStep = result.nextSteps[0];
        this.logExecution(execution, 'info', `Moving to step: ${result.nextSteps[0]}`);
      } else {
        // Handle parallel execution (simplified - just take first step for now)
        execution.currentStep = result.nextSteps[0];
        this.logExecution(execution, 'info', `Moving to step: ${result.nextSteps[0]} (parallel execution not fully implemented)`);
      }

    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.completedAt = new Date();
      this.logExecution(execution, 'error', `Step execution error: ${execution.error}`);
    }

    this.executions.set(execution.id, execution);
  }

  private async executeStep(
    step: WorkflowStep, 
    execution: WorkflowExecution, 
    workflow: Workflow
  ): Promise<WorkflowStepResult> {
    this.logExecution(execution, 'info', `Executing step: ${step.name} (${step.type})`);

    const handler = this.stepHandlers.get(step.type);
    if (!handler) {
      throw new Error(`No handler found for step type: ${step.type}`);
    }

    const context = {
      step,
      execution,
      workflow,
      variables: execution.variables
    };

    return await handler.execute(context);
  }

  private initializeWorkflowVariables(workflow: Workflow): Record<string, any> {
    const variables: Record<string, any> = {};
    
    for (const variable of workflow.variables) {
      if (variable.defaultValue !== undefined) {
        variables[variable.name] = variable.defaultValue;
      }
    }

    return variables;
  }

  private logExecution(execution: WorkflowExecution, level: 'info' | 'warning' | 'error', message: string, data?: any): void {
    const logEntry: WorkflowLogEntry = {
      timestamp: new Date(),
      stepId: execution.currentStep,
      level,
      message,
      data
    };

    execution.executionLog.push(logEntry);
    console.log(`[Workflow ${execution.id}] ${level.toUpperCase()}: ${message}`, data);
  }

  private generateExecutionId(): string {
    return `wf_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async handleEvent(eventType: string, eventData: any): Promise<void> {
    // Check for workflows that should be triggered by this event
    for (const [workflowId, workflow] of this.workflows) {
      if (workflow.status !== 'active') continue;

      for (const trigger of workflow.triggers) {
        if (trigger.enabled && trigger.type === 'event' && trigger.config.eventType === eventType) {
          try {
            const context: AutomationContext = {
              event: { type: eventType, data: eventData },
              timestamp: new Date(),
              executionId: this.generateExecutionId()
            };

            await this.startWorkflow(workflowId, context);
          } catch (error) {
            console.error(`Error starting workflow ${workflowId} from event ${eventType}:`, error);
          }
        }
      }
    }
  }

  private registerBuiltInHandlers(): void {
    // Action step handler
    this.stepHandlers.set('action', new ActionStepHandler());
    
    // Condition step handler
    this.stepHandlers.set('condition', new ConditionStepHandler());
    
    // Delay step handler
    this.stepHandlers.set('delay', new DelayStepHandler());
    
    // Webhook step handler
    this.stepHandlers.set('webhook', new WebhookStepHandler());
  }

  stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
  }
}

// Step handler interface
interface WorkflowStepHandler {
  execute(context: WorkflowStepContext): Promise<WorkflowStepResult>;
}

interface WorkflowStepContext {
  step: WorkflowStep;
  execution: WorkflowExecution;
  workflow: Workflow;
  variables: Record<string, any>;
}

// Built-in step handlers
class ActionStepHandler implements WorkflowStepHandler {
  async execute(context: WorkflowStepContext): Promise<WorkflowStepResult> {
    const { step } = context;
    const config = step.config;

    switch (config.actionType) {
      case 'send_message':
        return await this.sendMessage(config, context);
      case 'set_variable':
        return this.setVariable(config, context);
      default:
        throw new Error(`Unknown action type: ${config.actionType}`);
    }
  }

  private async sendMessage(config: any, context: WorkflowStepContext): Promise<WorkflowStepResult> {
    try {
      const response = await messageService.sendMessage({
        content: this.processTemplate(config.content || '', context.variables),
        channelId: config.channelId || context.variables.channelId
      });

      return {
        success: response.success,
        nextSteps: context.step.nextSteps,
        error: response.success ? undefined : response.error
      };
    } catch (error) {
      return {
        success: false,
        nextSteps: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private setVariable(config: any, context: WorkflowStepContext): WorkflowStepResult {
    const variables: Record<string, any> = {};
    
    if (config.variableName && config.variableValue !== undefined) {
      variables[config.variableName] = this.processTemplate(config.variableValue, context.variables);
    }

    return {
      success: true,
      nextSteps: context.step.nextSteps,
      variables
    };
  }

  private processTemplate(template: string, variables: Record<string, any>): string {
    let result = template;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }

    return result;
  }
}

class ConditionStepHandler implements WorkflowStepHandler {
  async execute(context: WorkflowStepContext): Promise<WorkflowStepResult> {
    const { step } = context;
    const config = step.config;
    const conditions = config.conditions || [];
    const operator = config.operator || 'AND';

    let result = operator === 'AND';

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, context.variables);
      
      if (operator === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }
    }

    return {
      success: true,
      nextSteps: result ? context.step.nextSteps : [],
    };
  }

  private evaluateCondition(condition: WorkflowCondition, variables: Record<string, any>): boolean {
    const fieldValue = variables[condition.field];
    const conditionValue = condition.value;

    switch (condition.operator) {
      case 'equals':
        return fieldValue === conditionValue;
      case 'not_equals':
        return fieldValue !== conditionValue;
      case 'contains':
        return String(fieldValue).includes(String(conditionValue));
      case 'greater_than':
        return Number(fieldValue) > Number(conditionValue);
      case 'less_than':
        return Number(fieldValue) < Number(conditionValue);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'not_exists':
        return fieldValue === undefined || fieldValue === null;
      default:
        return false;
    }
  }
}

class DelayStepHandler implements WorkflowStepHandler {
  async execute(context: WorkflowStepContext): Promise<WorkflowStepResult> {
    const { step } = context;
    const config = step.config;
    const delayMs = config.delayDuration || 1000;

    await new Promise(resolve => setTimeout(resolve, delayMs));

    return {
      success: true,
      nextSteps: context.step.nextSteps
    };
  }
}

class WebhookStepHandler implements WorkflowStepHandler {
  async execute(context: WorkflowStepContext): Promise<WorkflowStepResult> {
    const { step } = context;
    const config = step.config;

    try {
      const response = await fetch(config.url, {
        method: config.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        body: JSON.stringify(config.body || context.variables)
      });

      const responseData = await response.json();

      return {
        success: response.ok,
        nextSteps: context.step.nextSteps,
        variables: { webhookResponse: responseData },
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      };
    } catch (error) {
      return {
        success: false,
        nextSteps: [],
        error: error instanceof Error ? error.message : 'Webhook request failed'
      };
    }
  }
}

// Export singleton instance
export const workflowEngine = new WorkflowEngine();
