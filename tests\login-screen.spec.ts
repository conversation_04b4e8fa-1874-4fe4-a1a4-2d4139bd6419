import { test, expect } from '@playwright/test';

test.describe('Login Screen', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load the login screen without errors', async ({ page }) => {
    // Check that the page loads without console errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Wait for the login screen to be visible
    await expect(page.getByTestId('login-page')).toBeVisible();

    // Check for the main heading
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible();

    // Check for email and password inputs
    await expect(page.getByRole('textbox', { name: /email/i })).toBeVisible();
    await expect(
      page.getByRole('textbox', { name: /password/i })
    ).toBeVisible();

    // Check for login button
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();

    // Verify no console errors occurred
    expect(errors).toHaveLength(0);
  });

  test('should display proper error for invalid credentials', async ({
    page,
  }) => {
    // Fill in invalid credentials
    await page
      .getByRole('textbox', { name: /email/i })
      .fill('<EMAIL>');
    await page
      .getByRole('textbox', { name: /password/i })
      .fill('wrongpassword');

    // Click login button
    await page.getByRole('button', { name: /sign in/i }).click();

    // Wait for and check error message
    // Note: This depends on your actual error handling implementation
    // You may need to adjust the selector based on how errors are displayed
    await expect(page.locator('[role="alert"]')).toBeVisible({ timeout: 5000 });
  });

  test('should validate required fields', async ({ page }) => {
    // Try to submit without filling fields
    await page.getByRole('button', { name: /sign in/i }).click();

    // Check for validation messages
    // Note: Adjust selectors based on your validation implementation
    const emailInput = page.getByRole('textbox', { name: /email/i });
    const passwordInput = page.getByRole('textbox', { name: /password/i });

    // Check if HTML5 validation or custom validation is triggered
    await expect(emailInput).toHaveAttribute('aria-invalid', 'true');
    await expect(passwordInput).toHaveAttribute('aria-invalid', 'true');
  });
});

test.describe('Theme Toggle', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should have theme toggle button visible', async ({ page }) => {
    // Wait for the page to load
    await expect(page.getByTestId('login-page')).toBeVisible();

    // Check that theme toggle button is present
    const themeToggle = page.getByRole('button', { name: /theme/i });
    await expect(themeToggle).toBeVisible();
  });

  test('should toggle between themes when clicked', async ({ page }) => {
    // Wait for the page to load
    await expect(page.getByTestId('login-page')).toBeVisible();

    // Get the theme toggle button
    const themeToggle = page.getByRole('button', { name: /theme/i });
    await expect(themeToggle).toBeVisible();

    // Get initial theme state from document class
    const initialTheme = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark')
        ? 'dark'
        : 'light';
    });

    // Click the theme toggle
    await themeToggle.click();

    // Wait a bit for the theme change animation
    await page.waitForTimeout(200);

    // Check that theme has changed
    const newTheme = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark')
        ? 'dark'
        : 'light';
    });

    expect(newTheme).not.toBe(initialTheme);
  });

  test('should persist theme preference', async ({ page }) => {
    // Wait for the page to load
    await expect(page.getByTestId('login-page')).toBeVisible();

    // Toggle theme
    const themeToggle = page.getByRole('button', { name: /theme/i });
    await themeToggle.click();
    await page.waitForTimeout(200);

    // Get current theme
    const currentTheme = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark')
        ? 'dark'
        : 'light';
    });

    // Reload the page
    await page.reload();
    await expect(page.getByTestId('login-page')).toBeVisible();

    // Check that theme is still the same
    const persistedTheme = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark')
        ? 'dark'
        : 'light';
    });

    expect(persistedTheme).toBe(currentTheme);
  });

  test('should work without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    await expect(page.getByTestId('login-page')).toBeVisible();

    // Click theme toggle multiple times
    const themeToggle = page.getByRole('button', { name: /theme/i });
    await themeToggle.click();
    await page.waitForTimeout(200);
    await themeToggle.click();
    await page.waitForTimeout(200);
    await themeToggle.click();
    await page.waitForTimeout(200);

    // Verify no console errors occurred
    expect(errors).toHaveLength(0);
  });
});
