import React, { useState, useRef } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface FilePreviewProps {
  file: {
    id: string;
    name: string;
    type: 'image' | 'video' | 'audio' | 'document' | 'other';
    url: string;
    size: number;
    mimeType: string;
    thumbnail?: string;
  };
  showDetails?: boolean;
  allowDownload?: boolean;
  allowFullscreen?: boolean;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
  onRemove?: (fileId: string) => void;
  'data-testid'?: string;
}

export const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  showDetails = true,
  allowDownload = true,
  allowFullscreen = true,
  maxWidth = 400,
  maxHeight = 300,
  className = '',
  onRemove,
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string, mimeType: string) => {
    if (type === 'image') return '🖼️';
    if (type === 'video') return '🎥';
    if (type === 'audio') return '🎵';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📽️';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '🗜️';
    return '📎';
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFullscreen = () => {
    if (allowFullscreen) {
      setIsFullscreen(!isFullscreen);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleError = () => {
    setIsLoading(false);
    setError('Failed to load file');
  };

  const renderImagePreview = () => (
    <div className="relative">
      {isLoading && (
        <div
          className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg"
          style={{ minHeight: '150px' }}
        >
          <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      )}
      {error ? (
        <div
          className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-8"
          style={{ minHeight: '150px' }}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">❌</div>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              {error}
            </p>
          </div>
        </div>
      ) : (
        <img
          src={file.url}
          alt={file.name}
          className={`rounded-lg object-contain cursor-pointer transition-all duration-200 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          style={{
            maxWidth: isFullscreen ? '90vw' : `${maxWidth}px`,
            maxHeight: isFullscreen ? '90vh' : `${maxHeight}px`,
          }}
          onLoad={handleLoad}
          onError={handleError}
          onClick={handleFullscreen}
        />
      )}
      {allowFullscreen && !isLoading && !error && (
        <button
          onClick={handleFullscreen}
          className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-opacity"
        >
          {isFullscreen ? '🗗' : '🔍'}
        </button>
      )}
    </div>
  );

  const renderVideoPreview = () => (
    <div className="relative">
      <video
        ref={videoRef}
        controls
        className="rounded-lg"
        style={{
          maxWidth: isFullscreen ? '90vw' : `${maxWidth}px`,
          maxHeight: isFullscreen ? '90vh' : `${maxHeight}px`,
        }}
        onLoadedData={handleLoad}
        onError={handleError}
      >
        <source src={file.url} type={file.mimeType} />
        Your browser does not support the video tag.
      </video>
      {allowFullscreen && (
        <button
          onClick={handleFullscreen}
          className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-opacity"
        >
          {isFullscreen ? '🗗' : '🔍'}
        </button>
      )}
    </div>
  );

  const renderAudioPreview = () => (
    <div
      className="flex items-center space-x-3 p-4 border rounded-lg"
      style={{
        borderColor: colors.border,
        backgroundColor: colors.backgroundSecondary,
      }}
    >
      <div className="text-3xl">{getFileIcon(file.type, file.mimeType)}</div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
          {file.name}
        </p>
        <audio
          controls
          className="w-full mt-2"
          onLoadedData={handleLoad}
          onError={handleError}
        >
          <source src={file.url} type={file.mimeType} />
          Your browser does not support the audio tag.
        </audio>
      </div>
    </div>
  );

  const renderDocumentPreview = () => (
    <div
      className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
      style={{
        borderColor: colors.border,
        backgroundColor: colors.backgroundSecondary,
      }}
      onClick={() => window.open(file.url, '_blank')}
    >
      <div className="text-3xl">{getFileIcon(file.type, file.mimeType)}</div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
          {file.name}
        </p>
        <p className="text-xs" style={{ color: colors.textSecondary }}>
          {formatFileSize(file.size)} • Click to open
        </p>
      </div>
      <div className="text-lg" style={{ color: colors.primary }}>
        📖
      </div>
    </div>
  );

  const renderPreview = () => {
    switch (file.type) {
      case 'image':
        return renderImagePreview();
      case 'video':
        return renderVideoPreview();
      case 'audio':
        return renderAudioPreview();
      default:
        return renderDocumentPreview();
    }
  };

  return (
    <>
      <div className={`relative ${className}`} data-testid={testId}>
        {renderPreview()}

        {/* File Actions */}
        <div className="flex items-center justify-between mt-2">
          {showDetails && (
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium truncate" style={{ color: colors.text }}>
                {file.name}
              </p>
              <p className="text-xs" style={{ color: colors.textSecondary }}>
                {formatFileSize(file.size)}
              </p>
            </div>
          )}

          <div className="flex items-center space-x-2">
            {allowDownload && (
              <button
                onClick={handleDownload}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                style={{ color: colors.textSecondary }}
                title="Download"
              >
                ⬇️
              </button>
            )}
            {onRemove && (
              <button
                onClick={() => onRemove(file.id)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors text-red-500"
                title="Remove"
              >
                🗑️
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={() => setIsFullscreen(false)}
        >
          <div className="relative max-w-full max-h-full p-4">
            <button
              onClick={() => setIsFullscreen(false)}
              className="absolute top-2 right-2 bg-white bg-opacity-20 text-white p-2 rounded-full hover:bg-opacity-30 transition-opacity z-10"
            >
              ✕
            </button>
            {file.type === 'image' ? renderImagePreview() : renderVideoPreview()}
          </div>
        </div>
      )}
    </>
  );
};
