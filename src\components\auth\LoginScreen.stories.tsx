import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { LoginScreen } from './LoginScreen';

const meta: Meta<typeof LoginScreen> = {
  title: 'Auth/LoginScreen',
  component: LoginScreen,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A comprehensive login screen with split-screen layout, side-swapping functionality, and multiple authentication modes. Features responsive design, theme support, and smooth animations.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onLogin: {
      action: 'login',
      description: 'Callback function called when user attempts to login',
    },
    onAccessRequest: {
      action: 'access-request',
      description: 'Callback function called when user submits access request',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithCallbacks: Story = {
  args: {
    onLogin: credentials => {
      console.log('Login attempt:', credentials);
    },
    onAccessRequest: request => {
      console.log('Access request:', request);
    },
  },
};

// Story to demonstrate the component in different viewport sizes
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
  args: {},
};

export const Tablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
  args: {},
};

export const Desktop: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'desktop',
    },
  },
  args: {},
};
