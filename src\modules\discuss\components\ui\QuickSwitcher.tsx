import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { UserAvatar } from './UserPresence';
import type { Channel, User, Message } from '../../types';

export interface QuickSwitcherItem {
  id: string;
  type: 'channel' | 'user' | 'message' | 'command';
  title: string;
  subtitle?: string;
  icon?: string;
  avatar?: string;
  data?: Channel | User | Message | any;
  score?: number;
}

export interface QuickSwitcherProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (item: QuickSwitcherItem) => void;
  channels?: Channel[];
  users?: User[];
  recentMessages?: Message[];
  placeholder?: string;
  maxResults?: number;
  className?: string;
  'data-testid'?: string;
}

export const QuickSwitcher: React.FC<QuickSwitcherProps> = ({
  isOpen,
  onClose,
  onSelect,
  channels = [],
  users = [],
  recentMessages = [],
  placeholder = 'Search channels, users, or messages...',
  maxResults = 10,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [results, setResults] = useState<QuickSwitcherItem[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % results.length);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => (prev - 1 + results.length) % results.length);
          break;
        case 'Enter':
          e.preventDefault();
          if (results[selectedIndex]) {
            onSelect(results[selectedIndex]);
            onClose();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, results, onSelect, onClose]);

  // Scroll selected item into view
  useEffect(() => {
    const selectedItem = itemRefs.current[selectedIndex];
    if (selectedItem && listRef.current) {
      const container = listRef.current;
      const item = selectedItem;
      
      const containerTop = container.scrollTop;
      const containerBottom = containerTop + container.clientHeight;
      const itemTop = item.offsetTop;
      const itemBottom = itemTop + item.clientHeight;

      if (itemTop < containerTop) {
        container.scrollTop = itemTop;
      } else if (itemBottom > containerBottom) {
        container.scrollTop = itemBottom - container.clientHeight;
      }
    }
  }, [selectedIndex]);

  // Search and filter items
  const searchItems = useCallback((searchQuery: string): QuickSwitcherItem[] => {
    const lowerQuery = searchQuery.toLowerCase();
    const items: QuickSwitcherItem[] = [];

    // Add commands if query starts with /
    if (searchQuery.startsWith('/')) {
      const commands = [
        { command: '/join', description: 'Join a channel' },
        { command: '/leave', description: 'Leave current channel' },
        { command: '/mute', description: 'Mute notifications' },
        { command: '/unmute', description: 'Unmute notifications' },
        { command: '/status', description: 'Set your status' },
        { command: '/away', description: 'Set status to away' },
        { command: '/busy', description: 'Set status to busy' },
        { command: '/online', description: 'Set status to online' },
      ];

      commands.forEach(cmd => {
        if (cmd.command.toLowerCase().includes(lowerQuery)) {
          items.push({
            id: cmd.command,
            type: 'command',
            title: cmd.command,
            subtitle: cmd.description,
            icon: '⚡',
            data: cmd,
            score: cmd.command.toLowerCase().indexOf(lowerQuery),
          });
        }
      });
    } else {
      // Search channels
      channels.forEach(channel => {
        const nameMatch = channel.name.toLowerCase().includes(lowerQuery);
        const descMatch = channel.description?.toLowerCase().includes(lowerQuery);
        
        if (nameMatch || descMatch) {
          items.push({
            id: `channel-${channel.id}`,
            type: 'channel',
            title: `#${channel.name}`,
            subtitle: channel.description,
            icon: channel.type === 'private' ? '🔒' : '#',
            data: channel,
            score: nameMatch ? channel.name.toLowerCase().indexOf(lowerQuery) : 100,
          });
        }
      });

      // Search users
      users.forEach(user => {
        const nameMatch = user.name.toLowerCase().includes(lowerQuery);
        const emailMatch = user.email.toLowerCase().includes(lowerQuery);
        
        if (nameMatch || emailMatch) {
          items.push({
            id: `user-${user.id}`,
            type: 'user',
            title: user.name,
            subtitle: user.email,
            avatar: user.avatar,
            data: user,
            score: nameMatch ? user.name.toLowerCase().indexOf(lowerQuery) : 100,
          });
        }
      });

      // Search recent messages
      recentMessages.forEach(message => {
        if (message.content.toLowerCase().includes(lowerQuery)) {
          items.push({
            id: `message-${message.id}`,
            type: 'message',
            title: message.content.length > 50 
              ? `${message.content.substring(0, 50)}...` 
              : message.content,
            subtitle: `From ${message.authorId} • ${new Date(message.timestamp).toLocaleDateString()}`,
            icon: '💬',
            data: message,
            score: message.content.toLowerCase().indexOf(lowerQuery),
          });
        }
      });
    }

    // Sort by score and limit results
    return items
      .sort((a, b) => (a.score || 0) - (b.score || 0))
      .slice(0, maxResults);
  }, [channels, users, recentMessages, maxResults]);

  // Update results when query changes
  useEffect(() => {
    if (query.trim()) {
      const newResults = searchItems(query);
      setResults(newResults);
      setSelectedIndex(0);
    } else {
      // Show recent items when no query
      const recentItems: QuickSwitcherItem[] = [
        ...channels.slice(0, 3).map(channel => ({
          id: `channel-${channel.id}`,
          type: 'channel' as const,
          title: `#${channel.name}`,
          subtitle: channel.description,
          icon: channel.type === 'private' ? '🔒' : '#',
          data: channel,
        })),
        ...users.slice(0, 3).map(user => ({
          id: `user-${user.id}`,
          type: 'user' as const,
          title: user.name,
          subtitle: user.email,
          avatar: user.avatar,
          data: user,
        })),
      ];
      setResults(recentItems);
      setSelectedIndex(0);
    }
  }, [query, searchItems, channels, users]);

  const getItemIcon = (item: QuickSwitcherItem) => {
    if (item.type === 'user' && item.avatar) {
      return (
        <UserAvatar
          user={item.data as User}
          size="small"
          showStatus={true}
        />
      );
    }
    
    return (
      <div
        className="w-6 h-6 rounded flex items-center justify-center text-sm"
        style={{
          backgroundColor: `${colors.primary}20`,
          color: colors.primary,
        }}
      >
        {item.icon}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-20 z-50"
      onClick={onClose}
      data-testid={testId}
    >
      <div
        className={`w-full max-w-2xl mx-4 rounded-lg shadow-xl border ${className}`}
        style={{
          backgroundColor: colors.background,
          borderColor: colors.border,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Search Input */}
        <div className="p-4 border-b" style={{ borderBottomColor: colors.border }}>
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={placeholder}
            className="w-full text-lg bg-transparent outline-none"
            style={{ color: colors.text }}
          />
        </div>

        {/* Results */}
        <div
          ref={listRef}
          className="max-h-96 overflow-y-auto"
        >
          {results.length > 0 ? (
            <div className="py-2">
              {results.map((item, index) => (
                <div
                  key={item.id}
                  ref={el => itemRefs.current[index] = el}
                  onClick={() => {
                    onSelect(item);
                    onClose();
                  }}
                  className={`flex items-center space-x-3 px-4 py-3 cursor-pointer transition-colors ${
                    index === selectedIndex ? 'bg-gray-100 dark:bg-gray-700' : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  {/* Icon/Avatar */}
                  <div className="flex-shrink-0">
                    {getItemIcon(item)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate" style={{ color: colors.text }}>
                      {item.title}
                    </div>
                    {item.subtitle && (
                      <div className="text-sm truncate" style={{ color: colors.textSecondary }}>
                        {item.subtitle}
                      </div>
                    )}
                  </div>

                  {/* Type Badge */}
                  <div
                    className="text-xs px-2 py-1 rounded"
                    style={{
                      backgroundColor: colors.backgroundSecondary,
                      color: colors.textSecondary,
                    }}
                  >
                    {item.type}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center">
              <div className="text-4xl mb-2">🔍</div>
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                {query ? `No results for "${query}"` : 'Start typing to search...'}
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div
          className="px-4 py-2 border-t text-xs"
          style={{
            borderTopColor: colors.border,
            color: colors.textSecondary,
          }}
        >
          <div className="flex items-center justify-between">
            <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
            <span>Type / for commands</span>
          </div>
        </div>
      </div>
    </div>
  );
};
