/**
 * ErrorBoundary Module Exports
 * Comprehensive error boundary system with enhanced features
 */

// Main components
export { default as ErrorBoundary } from './ErrorBoundary';
export type { ErrorBoundaryProps, ErrorFallbackProps } from './ErrorBoundary';

export { default as ErrorFallback } from '../ErrorFallback/ErrorFallback';
export type { ErrorFallbackProps as ErrorFallbackComponentProps } from '../ErrorFallback/ErrorFallback';

export {
  default as ErrorBoundaryProvider,
  useErrorBoundaryContext,
  useGlobalErrorHandler,
  withErrorBoundary
} from './ErrorBoundaryProvider';
export type { 
  ErrorBoundaryProviderProps,
  ErrorBoundaryContextValue 
} from './ErrorBoundaryProvider';

// Hooks
export {
  useErrorBoundary,
  useErrorBoundaryState,
  useAsyncError,
  useErrorHandler
} from './useErrorBoundary';
export type {
  UseErrorBoundaryOptions,
  UseErrorBoundaryReturn
} from './useErrorBoundary';

// Utility types and functions
export type {
  AppError,
  ErrorType,
  ErrorSeverity,
  ErrorContext,
  RecoveryAction
} from '../../../utils/errorTypes';

export {
  createAppError,
  classifyError,
  shouldAutoRetry,
  getRetryDelay
} from '../../../utils/errorTypes';

// Recovery system
export type { 
  RecoveryStrategy,
  RecoveryContext 
} from '../../../utils/errorRecovery';

export {
  getRecoveryStrategies,
  executeAutoRecovery,
  executeRecoveryStrategy
} from '../../../utils/errorRecovery';

// Reporting system
export {
  reportError,
  configureErrorReporting,
  getErrorStats
} from '../../../utils/errorReporting';
export type {
  ErrorReportingConfig,
  ErrorReport
} from '../../../utils/errorReporting';