# Theme System Documentation

## Overview

The Nexed Web application features a comprehensive, robust theme system built with Zustand, Tailwind CSS v4, and CSS custom properties. The system provides consistent theming across the entire application with support for light/dark modes, system preference detection, and theme switching capabilities. **This system uses pure Zustand state management without React Context for optimal performance and simplicity.**

## Architecture

### Core Components

1. **Theme Store** (`src/stores/themeStore.ts`)
   - Zustand store for theme state management
   - Comprehensive color palette with semantic naming
   - System preference detection and persistence
   - CSS variable generation and application
   - Built-in initialization and cleanup functionality

2. **Theme Hooks** (`src/hooks/useTheme.ts`)
   - Pure Zustand-based hooks for theme access
   - No React Context dependency
   - Optimized performance with selective subscriptions
   - Complete theme functionality access

3. **Theme Utilities** (`src/utils/themeUtils.ts`)
   - Color manipulation utilities
   - Accessibility validation
   - Theme persistence helpers
   - Animation utilities

4. **Tailwind Configuration** (`tailwind.config.ts`)
   - CSS custom property integration
   - Comprehensive color system
   - Design tokens and utilities

## Features

### Theme Modes

- **Light Mode**: Professional light color scheme
- **Dark Mode**: Professional dark color scheme
- **System Mode**: Automatically follows system preference

### Theme Variants

- **Default**: Standard theme appearance
- **High Contrast**: Enhanced contrast for accessibility
- **Colorblind Friendly**: Optimized for color vision deficiency

### Color System

#### Core Brand Colors

- `primary` / `primary-foreground`
- `secondary` / `secondary-foreground`
- `accent` / `accent-foreground`
- `neutral`

#### Semantic Colors

- `success` / `success-foreground`
- `warning` / `warning-foreground`
- `error` / `error-foreground`
- `info` / `info-foreground`

#### Background Colors

- `background` / `foreground`
- `surface` / `surface-secondary` / `surface-tertiary`

#### Text Colors

- `text` / `text-secondary` / `text-muted` / `text-inverse`

#### Interactive States

- `hover` / `active` / `focus`
- `disabled` / `disabled-foreground`

#### Component Colors

- `muted` / `muted-foreground`
- `destructive` / `destructive-foreground`
- `card` / `card-foreground`
- `input` / `input-foreground`
- `popover` / `popover-foreground`

#### Utility Colors

- `border` / `border-secondary` / `border-focus`
- `ring` / `shadow` / `overlay`
- `chart-1` through `chart-5`

## Usage

### Basic Setup

```tsx
import { useThemeStore } from './stores/themeStore';

// Initialize theme in main.tsx
useThemeStore.getState().initialize({
  defaultTheme: 'system',
  enableSystemDetection: true,
  enableCSSVariables: true,
});

function App() {
  return <>{/* Your app content - no provider needed! */}</>;
}
```

### Using Theme in Components

```tsx
import { useTheme } from './hooks/useTheme';

function MyComponent() {
  const { theme, colors, setTheme, isDark } = useTheme();

  return (
    <div className="bg-surface text-text border border-border">
      <button
        onClick={() => setTheme(isDark ? 'light' : 'dark')}
        className="bg-primary text-primary-foreground hover:bg-primary/90"
      >
        Toggle Theme
      </button>
    </div>
  );
}
```

### Tailwind CSS Classes

The theme system provides Tailwind classes that automatically use CSS variables:

```tsx
// Background colors
<div className="bg-background">
<div className="bg-surface">
<div className="bg-primary">

// Text colors
<p className="text-text">
<p className="text-text-secondary">
<p className="text-primary-foreground">

// Border colors
<div className="border border-border">
<div className="border-2 border-primary">

// Interactive states
<button className="hover:bg-hover active:bg-active">
```

### CSS Custom Properties

All theme colors are available as CSS custom properties:

```css
.my-component {
  background-color: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.my-component:hover {
  background-color: var(--color-hover);
}
```

### Theme Utilities

```tsx
import { ColorUtils, ThemeDetection } from '../utils/themeUtils';

// Color manipulation
const lighterColor = ColorUtils.lighten('#2563eb', 20);
const contrastRatio = ColorUtils.getContrastRatio('#ffffff', '#000000');
const isAccessible = ColorUtils.isAccessible('#ffffff', '#2563eb');

// System detection
const systemTheme = ThemeDetection.getSystemTheme();
const prefersHighContrast = ThemeDetection.prefersHighContrast();
```

## Accessibility

The theme system includes comprehensive accessibility features:

### Color Contrast

- All color combinations meet WCAG AA standards
- Automatic contrast validation
- High contrast variant available

### System Preferences

- Respects `prefers-color-scheme`
- Supports `prefers-contrast: high`
- Honors `prefers-reduced-motion`

### Focus Management

- Consistent focus indicators
- High visibility focus rings
- Keyboard navigation support

## Customization

### Adding New Colors

1. Update the `ThemeColors` interface in `src/stores/themeStore.ts`
2. Add colors to both `lightTheme` and `darkTheme` objects
3. Update CSS variables in `src/index.css`
4. Add Tailwind classes in `tailwind.config.ts`

### Creating Theme Variants

```tsx
// In your theme store
const highContrastTheme: ThemeColors = {
  // Enhanced contrast colors
  primary: '#0000ff',
  background: '#ffffff',
  text: '#000000',
  // ... other colors
};

// Usage
const { setVariant } = useTheme();
setVariant('high-contrast');
```

### Custom Theme Animations

```tsx
import { ThemeAnimations } from '../utils/themeUtils';

// Smooth theme transition
ThemeAnimations.createThemeTransition(300);

// Animated theme change
ThemeAnimations.animateThemeChange(() => {
  setTheme('dark');
});
```

## Best Practices

1. **Use Semantic Colors**: Always use semantic color names (`primary`, `success`) rather than specific colors (`blue`, `green`)

2. **Consistent Patterns**: Follow the established foreground/background pairing pattern

3. **Accessibility First**: Test all color combinations for sufficient contrast

4. **System Integration**: Respect user system preferences when possible

5. **Performance**: CSS variables provide optimal performance for theme switching

6. **Testing**: Test themes in both light and dark modes across all components

## Migration Guide

### From Old Theme System

If migrating from a previous theme implementation:

1. Replace `useThemeStore` with `useTheme` from ThemeProvider
2. Update color references to use new semantic names
3. Replace inline styles with Tailwind classes where possible
4. Update CSS to use CSS custom properties

### Component Updates

```tsx
// Before
const { colors } = useThemeStore();
<div style={{ backgroundColor: colors.primary }}>

// After
<div className="bg-primary">
```

## Troubleshooting

### Common Issues

1. **Colors not updating**: Ensure theme store is initialized in main.tsx
2. **CSS variables undefined**: Check that `enableCSSVariables` is true in initialization
3. **System theme not detected**: Verify `enableSystemDetection` is enabled in initialization
4. **Flashing on load**: CSS variables are applied in index.css as fallbacks
5. **Hook errors**: Make sure you're importing from `./hooks/useTheme` not the old provider paths

### Debug Tools

```tsx
// Check current theme state
const theme = useTheme();
console.log('Current theme:', theme);

// Validate theme colors
import { ThemeValidation } from '../utils/themeUtils';
const validation = ThemeValidation.validateThemeColors(theme.colors);
console.log('Theme validation:', validation);
```

## Performance Considerations

- CSS custom properties provide optimal theme switching performance
- Theme state is persisted automatically via Zustand persistence
- System preference listeners are cleaned up automatically
- Smooth transitions prevent jarring theme changes

## Browser Support

- Modern browsers with CSS custom property support
- Graceful fallback for older browsers via default CSS values
- Media query support for system preference detection
