import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { callService } from '../../services/callService';
import type { Call } from '../../types';

export interface MeetingRecordingProps {
  call: Call;
  currentUserId: string;
  isRecording: boolean;
  onStartRecording: () => void;
  onStopRecording: () => void;
  className?: string;
  'data-testid'?: string;
}

export const MeetingRecording: React.FC<MeetingRecordingProps> = ({
  call,
  currentUserId,
  isRecording,
  onStartRecording,
  onStopRecording,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);

  // Update recording duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setRecordingDuration(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartRecording = async () => {
    try {
      setIsProcessing(true);
      await onStartRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStopRecording = async () => {
    try {
      setIsProcessing(true);
      await onStopRecording();
    } catch (error) {
      console.error('Failed to stop recording:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div 
      className={`flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border ${className}`}
      style={{ borderColor: colors.border }}
      data-testid={testId}
    >
      {/* Recording Status Indicator */}
      <div className="flex items-center space-x-2">
        {isRecording ? (
          <>
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-red-500">
              REC {formatDuration(recordingDuration)}
            </span>
          </>
        ) : (
          <>
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span className="text-sm" style={{ color: colors.textSecondary }}>
              Not recording
            </span>
          </>
        )}
      </div>

      {/* Recording Controls */}
      <div className="flex items-center space-x-2">
        {!isRecording ? (
          <button
            onClick={handleStartRecording}
            disabled={isProcessing}
            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
              isProcessing
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                : 'bg-red-500 hover:bg-red-600 text-white'
            }`}
            title="Start recording"
          >
            {isProcessing ? 'Starting...' : '⏺ Start Recording'}
          </button>
        ) : (
          <button
            onClick={handleStopRecording}
            disabled={isProcessing}
            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
              isProcessing
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 hover:bg-gray-700 text-white'
            }`}
            title="Stop recording"
          >
            {isProcessing ? 'Stopping...' : '⏹ Stop Recording'}
          </button>
        )}
      </div>

      {/* Recording Info */}
      {isRecording && (
        <div className="text-xs" style={{ color: colors.textSecondary }}>
          Recording will be saved automatically
        </div>
      )}
    </div>
  );
};

// Recording History Component
export interface RecordingHistoryProps {
  callId?: string;
  className?: string;
  'data-testid'?: string;
}

interface Recording {
  id: string;
  callId: string;
  name: string;
  duration: number;
  size: string;
  createdAt: Date;
  url: string;
}

export const RecordingHistory: React.FC<RecordingHistoryProps> = ({
  callId,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [recordings, setRecordings] = useState<Recording[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRecordings = async () => {
      try {
        setIsLoading(true);
        // Mock data for now
        const mockRecordings: Recording[] = [
          {
            id: '1',
            callId: callId || 'call-1',
            name: 'Team Standup - Jan 10',
            duration: 1800, // 30 minutes
            size: '45.2 MB',
            createdAt: new Date('2025-01-10T09:00:00'),
            url: '/recordings/team-standup-jan-10.mp4',
          },
          {
            id: '2',
            callId: callId || 'call-2',
            name: 'Project Review - Jan 9',
            duration: 3600, // 1 hour
            size: '120.5 MB',
            createdAt: new Date('2025-01-09T14:00:00'),
            url: '/recordings/project-review-jan-9.mp4',
          },
        ];
        setRecordings(mockRecordings);
      } catch (error) {
        console.error('Failed to fetch recordings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecordings();
  }, [callId]);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className={`p-4 ${className}`} data-testid={testId}>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`} data-testid={testId}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
        Meeting Recordings
      </h3>
      
      {recordings.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">🎥</div>
          <p className="text-lg font-medium mb-1" style={{ color: colors.text }}>
            No recordings yet
          </p>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Start recording your meetings to see them here
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {recordings.map(recording => (
            <div
              key={recording.id}
              className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border hover:shadow-md transition-shadow"
              style={{ borderColor: colors.border }}
            >
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                <span className="text-blue-600 dark:text-blue-400 text-xl">🎥</span>
              </div>
              
              <div className="flex-1">
                <h4 className="font-medium mb-1" style={{ color: colors.text }}>
                  {recording.name}
                </h4>
                <div className="flex items-center space-x-4 text-sm" style={{ color: colors.textSecondary }}>
                  <span>{formatDuration(recording.duration)}</span>
                  <span>{recording.size}</span>
                  <span>{formatDate(recording.createdAt)}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => window.open(recording.url, '_blank')}
                  className="p-2 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Play recording"
                >
                  ▶️
                </button>
                <button
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = recording.url;
                    link.download = `${recording.name}.mp4`;
                    link.click();
                  }}
                  className="p-2 text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Download recording"
                >
                  ⬇️
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
