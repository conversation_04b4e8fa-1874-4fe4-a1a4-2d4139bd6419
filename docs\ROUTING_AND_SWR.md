# Routing and SWR Implementation Guide

This document explains the robust folder-based routing and SWR data fetching implementation in the Nexed Web application.

## Overview

The application now features:

- **React Router v6** for declarative routing
- **SWR** for data fetching with caching, revalidation, and error handling
- **Folder-based route organization** for scalable architecture
- **Configuration-driven setup** using `app-config.yml`

## Folder Structure

```
src/
├── pages/                 # Route components
│   ├── index.tsx         # Home page (login screen)
│   ├── demo.tsx          # Demo page
│   └── layout.tsx        # Root layout component
├── router/               # Router configuration
│   └── index.tsx         # Router setup and route definitions
├── providers/            # Context providers
│   ├── SWRProvider.tsx   # SWR configuration provider
│   └── index.ts          # Provider exports
├── hooks/                # Custom hooks
│   └── useSWR.ts         # SWR-based data fetching hooks
└── utils/                # Utilities
    └── swrConfig.ts      # SWR configuration utilities
```

## Routing Implementation

### Route Configuration

Routes are defined in `src/router/index.tsx` using React Router v6's `createBrowserRouter`:

```typescript
const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'demo',
        element: <DemoPage />,
      },
    ],
  },
]);
```

### Adding New Routes

To add a new route:

1. Create a new page component in `src/pages/`
2. Add the route to the router configuration
3. Use React Router hooks for navigation

Example:

```typescript
// src/pages/settings.tsx
export default function SettingsPage() {
  return <div>Settings Page</div>;
}

// Add to router configuration
{
  path: 'settings',
  element: <SettingsPage />,
}
```

### Navigation

Use React Router hooks for navigation:

```typescript
import { useNavigate, Link } from 'react-router-dom';

// Programmatic navigation
const navigate = useNavigate();
navigate('/demo');

// Declarative navigation
<Link to="/demo">Go to Demo</Link>
```

## SWR Implementation

### Configuration

SWR is configured globally using the `SWRProvider` component, which reads settings from `app-config.yml`:

```typescript
// Automatic configuration based on app-config.yml
<SWRProvider>
  <AppRouter />
</SWRProvider>
```

### Key Features

1. **Automatic retries** with exponential backoff
2. **Request timeout** handling
3. **Error handling** with status-specific logic
4. **Optimistic updates** for better UX
5. **Real-time updates** with configurable intervals

### Custom Hooks

#### Basic Data Fetching

```typescript
import { useAPI } from '../hooks/useSWR';

function UserProfile() {
  const { data: user, error, isLoading } = useAPI('/user');

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return <div>Hello, {user.name}!</div>;
}
```

#### Users Management

```typescript
import { useUsers, useOptimisticMutation } from '../hooks/useSWR';

function UserList() {
  const { data: users, error, isLoading } = useUsers();
  const { mutateOptimistic } = useOptimisticMutation();

  const addUser = async userData => {
    const optimisticUser = { id: 'temp', ...userData };

    await mutateOptimistic(
      '/users',
      () => createUser(userData),
      [...users, optimisticUser],
      { rollbackOnError: true }
    );
  };

  // Component implementation...
}
```

#### Authentication

```typescript
import { useAuth } from '../hooks/useSWR';

function LoginForm() {
  const { login, isLoading, error } = useAuth();

  const handleSubmit = async credentials => {
    try {
      await login(credentials);
      navigate('/dashboard');
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Form implementation...
}
```

#### Real-time Data

```typescript
import { useRealTimeAPI } from '../hooks/useSWR';

function LiveData() {
  const { data, error } = useRealTimeAPI('/live-stats', 5000); // Poll every 5 seconds

  return <div>Live count: {data?.count}</div>;
}
```

### Configuration Options

The SWR configuration is driven by `app-config.yml`:

```yaml
# API Configuration
api:
  baseUrl: '/api'
  timeout: 30000
  retries: 3
  retryDelay: 1000

# Performance Settings
performance:
  enableCaching: true
  cacheTimeout: 300000

# Feature Flags
features:
  enableRealTimeUpdates: true
```

### Error Handling

SWR provides comprehensive error handling:

1. **Network errors** are automatically retried
2. **HTTP errors** (4xx, 5xx) are exposed to components
3. **Timeout errors** trigger automatic retries
4. **404/403 errors** don't trigger retries

### Cache Management

SWR automatically manages cache with:

1. **Deduplication** of identical requests
2. **Background revalidation** on focus/reconnect
3. **Stale-while-revalidate** strategy
4. **Manual cache invalidation** when needed

### Best Practices

1. **Use specific hooks** for different data types (`useUsers`, `useAuth`, etc.)
2. **Implement optimistic updates** for better UX
3. **Handle loading and error states** appropriately
4. **Use cache keys consistently** across the application
5. **Configure timeouts** based on API performance

## MSW Integration

The application uses MSW (Mock Service Worker) for API mocking during development:

```typescript
// src/mocks/handlers.ts
export const handlers = [
  http.get('/api/users', async () => {
    await delay(500); // Simulate network delay
    return HttpResponse.json(users);
  }),

  http.post('/api/users', async ({ request }) => {
    const userData = await request.json();
    const newUser = { id: Date.now().toString(), ...userData };
    users.push(newUser);
    return HttpResponse.json(newUser, { status: 201 });
  }),
];
```

## Testing

SWR provides excellent testing support:

```typescript
import { SWRConfig } from 'swr';

// Reset cache between tests
describe('UserList', () => {
  it('renders users', () => {
    render(
      <SWRConfig value={{ provider: () => new Map() }}>
        <UserList />
      </SWRConfig>
    );
  });
});
```

## Performance Considerations

1. **Request deduplication** prevents duplicate API calls
2. **Background revalidation** keeps data fresh without blocking UI
3. **Optimistic updates** provide immediate feedback
4. **Configurable cache timeouts** balance freshness and performance
5. **Exponential backoff** prevents API overload during errors

## Migration Guide

If migrating from Zustand-only state management:

1. Replace direct store calls with SWR hooks
2. Move API logic from stores to SWR hooks
3. Use optimistic updates for mutations
4. Configure SWR based on your API characteristics

This implementation provides a robust, scalable foundation for data fetching and routing in modern React applications.
