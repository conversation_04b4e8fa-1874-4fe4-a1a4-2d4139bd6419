import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { useCall } from '../../hooks/useCall';
import { CallInterface } from './CallInterface';
import { MeetingRecording } from './MeetingRecording';
import { getMockUserById } from '../../../../mocks/data/discuss';
import type { Call, User } from '../../types';

export interface ConferenceRoomProps {
  roomId: string;
  roomName: string;
  currentUserId: string;
  onLeaveRoom?: () => void;
  className?: string;
  'data-testid'?: string;
}

export const ConferenceRoom: React.FC<ConferenceRoomProps> = ({
  roomId,
  roomName,
  currentUserId,
  onLeaveRoom,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [activeCall, setActiveCall] = useState<Call | null>(null);
  const [waitingParticipants, setWaitingParticipants] = useState<User[]>([]);
  const [isJoining, setIsJoining] = useState(false);
  const [showRecordings, setShowRecordings] = useState(false);

  const {
    currentCall,
    participants,
    isInCall,
    isConnecting,
    callError,
    startCall,
    joinCall,
    leaveCall,
    startRecording,
    stopRecording,
    isRecording,
  } = useCall({ userId: currentUserId });

  // Mock waiting participants
  useEffect(() => {
    const mockWaiting: User[] = [
      getMockUserById('2'),
      getMockUserById('3'),
    ].filter(Boolean) as User[];
    setWaitingParticipants(mockWaiting);
  }, []);

  const handleJoinRoom = async () => {
    try {
      setIsJoining(true);
      
      // Check if there's an active call in the room
      const existingCallId = `room-${roomId}-call`;
      
      try {
        await joinCall(existingCallId);
      } catch {
        // No existing call, start a new one
        const call = await startCall('video', []);
        setActiveCall(call);
      }
    } catch (error) {
      console.error('Failed to join room:', error);
    } finally {
      setIsJoining(false);
    }
  };

  const handleLeaveRoom = async () => {
    try {
      await leaveCall();
      setActiveCall(null);
      onLeaveRoom?.();
    } catch (error) {
      console.error('Failed to leave room:', error);
    }
  };

  const handleAdmitParticipant = (userId: string) => {
    // In a real implementation, this would send an invitation
    setWaitingParticipants(prev => prev.filter(p => p.id !== userId));
    console.log('Admitting participant:', userId);
  };

  const handleDenyParticipant = (userId: string) => {
    setWaitingParticipants(prev => prev.filter(p => p.id !== userId));
    console.log('Denying participant:', userId);
  };

  if (isInCall && currentCall) {
    return (
      <div className={`h-full flex flex-col ${className}`} data-testid={testId}>
        {/* Room Header */}
        <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: colors.border }}>
          <div>
            <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
              {roomName}
            </h2>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Conference Room • {participants.length} participant{participants.length !== 1 ? 's' : ''}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowRecordings(!showRecordings)}
              className="px-3 py-1.5 text-sm rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              style={{ borderColor: colors.border, color: colors.text }}
            >
              📹 Recordings
            </button>
            <button
              onClick={handleLeaveRoom}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
            >
              Leave Room
            </button>
          </div>
        </div>

        {/* Recording Controls */}
        <div className="p-4 border-b" style={{ borderColor: colors.border }}>
          <MeetingRecording
            call={currentCall}
            currentUserId={currentUserId}
            isRecording={isRecording}
            onStartRecording={startRecording}
            onStopRecording={stopRecording}
          />
        </div>

        {/* Call Interface */}
        <div className="flex-1">
          <CallInterface
            call={currentCall}
            currentUserId={currentUserId}
            onEndCall={handleLeaveRoom}
          />
        </div>

        {/* Recordings Panel */}
        {showRecordings && (
          <div className="w-80 border-l bg-white dark:bg-gray-800 p-4" style={{ borderColor: colors.border }}>
            <button
              onClick={() => setShowRecordings(false)}
              className="float-right text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ✕
            </button>
            <div className="clear-both">
              {/* RecordingHistory component would go here */}
              <div className="text-center py-8">
                <div className="text-4xl mb-2">🎥</div>
                <p className="text-sm" style={{ color: colors.textSecondary }}>
                  Recording history will appear here
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`} data-testid={testId}>
      {/* Room Header */}
      <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: colors.border }}>
        <div>
          <h2 className="text-2xl font-semibold mb-2" style={{ color: colors.text }}>
            {roomName}
          </h2>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Conference Room ID: {roomId}
          </p>
        </div>
        
        {onLeaveRoom && (
          <button
            onClick={onLeaveRoom}
            className="px-4 py-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            style={{ borderColor: colors.border, color: colors.textSecondary }}
          >
            ← Back
          </button>
        )}
      </div>

      {/* Room Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          {/* Room Status */}
          <div className="w-24 h-24 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-4xl">🏢</span>
          </div>
          
          <h3 className="text-xl font-semibold mb-2" style={{ color: colors.text }}>
            Ready to join {roomName}?
          </h3>
          
          <p className="text-sm mb-6" style={{ color: colors.textSecondary }}>
            Click the button below to join the conference room. Your camera and microphone will be activated.
          </p>

          {/* Join Button */}
          <button
            onClick={handleJoinRoom}
            disabled={isJoining || isConnecting}
            className={`w-full py-3 px-6 rounded-lg text-white font-medium transition-colors ${
              isJoining || isConnecting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
          >
            {isJoining || isConnecting ? 'Joining...' : '📹 Join Conference Room'}
          </button>

          {/* Error Message */}
          {callError && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">
                {callError}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Waiting Participants */}
      {waitingParticipants.length > 0 && (
        <div className="border-t p-4" style={{ borderColor: colors.border }}>
          <h4 className="font-medium mb-3" style={{ color: colors.text }}>
            Waiting to Join ({waitingParticipants.length})
          </h4>
          <div className="space-y-2">
            {waitingParticipants.map(participant => (
              <div
                key={participant.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
                    {participant.name.charAt(0).toUpperCase()}
                  </div>
                  <span style={{ color: colors.text }}>{participant.name}</span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleAdmitParticipant(participant.id)}
                    className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white text-sm rounded transition-colors"
                  >
                    Admit
                  </button>
                  <button
                    onClick={() => handleDenyParticipant(participant.id)}
                    className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-sm rounded transition-colors"
                  >
                    Deny
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
