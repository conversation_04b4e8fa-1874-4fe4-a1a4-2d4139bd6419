// Test file for DiscussModule
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { DiscussModule } from '../DiscussModule';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#000000',
      textSecondary: '#6c757d',
      primary: '#007bff',
      border: '#dee2e6',
      backgroundSecondary: '#e9ecef',
    },
  }),
}));

// Mock the discuss data
vi.mock('../../../mocks/data/discuss', () => ({
  mockUsers: [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'JD',
      status: 'online',
      lastSeen: new Date(),
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'JS',
      status: 'away',
      lastSeen: new Date(),
    },
  ],
  mockChannels: [
    {
      id: 'general',
      name: 'general',
      description: 'General discussion',
      type: 'public',
      memberIds: ['1', '2'],
      createdBy: '1',
      createdAt: new Date(),
      lastActivity: new Date(),
      isArchived: false,
      settings: {
        notifications: true,
        allowFileUploads: true,
        allowExternalLinks: true,
      },
    },
  ],
  mockMessages: [
    {
      id: 'msg-1',
      content: 'Hello world!',
      authorId: '1',
      channelId: 'general',
      timestamp: new Date(),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read',
    },
  ],
  getMockMessagesByChannelId: vi.fn(() => []),
  getMockUserById: vi.fn(() => null),
}));

// Mock WebSocket service
vi.mock('../services/websocketService', () => ({
  websocketService: {
    connect: vi.fn(() => Promise.resolve()),
    disconnect: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    sendTyping: vi.fn(),
    updatePresence: vi.fn(),
    joinChannel: vi.fn(),
    leaveChannel: vi.fn(),
    isConnected: true,
    userId: '1',
  },
}));

// Mock message service
vi.mock('../services/messageService', () => ({
  messageService: {
    getChannelMessages: vi.fn(() => Promise.resolve({
      data: [],
      total: 0,
      page: 1,
      pageSize: 50,
      hasMore: false,
    })),
    sendMessage: vi.fn(() => Promise.resolve({
      success: true,
      data: {
        id: 'new-msg',
        content: 'Test message',
        authorId: '1',
        channelId: 'general',
        timestamp: new Date(),
        reactions: [],
        attachments: [],
        mentions: [],
        isDeleted: false,
        deliveryStatus: 'sent',
      },
    })),
    addReaction: vi.fn(() => Promise.resolve({ success: true })),
    removeReaction: vi.fn(() => Promise.resolve({ success: true })),
    updateMessage: vi.fn(() => Promise.resolve({ success: true })),
    deleteMessage: vi.fn(() => Promise.resolve({ success: true })),
  },
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('DiscussModule', () => {
  beforeEach(() => {
    // Mock URL search params
    Object.defineProperty(window, 'location', {
      value: {
        search: '?menu=10&view=channels',
      },
      writable: true,
    });

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the discuss module correctly', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check if the main elements are rendered
    expect(screen.getByText('Discuss')).toBeInTheDocument();
    expect(screen.getByText('📢 #general')).toBeInTheDocument();
  });

  it('displays the sidebar with navigation items', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check sidebar items
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(screen.getByText('Direct Messages')).toBeInTheDocument();
    expect(screen.getByText('Teams')).toBeInTheDocument();
  });

  it('shows the message input area', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check message input
    const messageInput = screen.getByPlaceholderText(/Type a message/);
    expect(messageInput).toBeInTheDocument();
  });

  it('allows typing in the message input', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/Type a message/);
    
    fireEvent.change(messageInput, { target: { value: 'Hello world!' } });
    
    expect(messageInput).toHaveValue('Hello world!');
  });

  it('shows channel header with correct information', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check channel header
    expect(screen.getByText('📢 #general')).toBeInTheDocument();
    expect(screen.getByText('42 members')).toBeInTheDocument();
  });

  it('displays action buttons in the header', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check action buttons
    expect(screen.getByTitle('Channel info')).toBeInTheDocument();
    expect(screen.getByTitle('Start call')).toBeInTheDocument();
    expect(screen.getByTitle('More options')).toBeInTheDocument();
  });

  it('shows message input toolbar', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Check toolbar items
    expect(screen.getByText('📎')).toBeInTheDocument(); // Attach
    expect(screen.getByText('😊')).toBeInTheDocument(); // Emoji
    expect(screen.getByText('📝')).toBeInTheDocument(); // Format
  });

  it('handles sidebar collapse/expand', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Find and click the collapse button
    const collapseButton = screen.getByText('←');
    fireEvent.click(collapseButton);

    // After collapse, the button should change to expand
    await waitFor(() => {
      expect(screen.getByText('→')).toBeInTheDocument();
    });
  });

  it('switches between different views', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Click on Direct Messages
    const dmButton = screen.getByText('Direct Messages');
    fireEvent.click(dmButton);

    // Should navigate to messages view
    // Note: In a real test, you'd check URL changes or view content changes
    expect(dmButton).toBeInTheDocument();
  });

  it('displays loading state correctly', async () => {
    // Mock loading state
    const mockMessageService = await import('../services/messageService');
    vi.mocked(mockMessageService.messageService.getChannelMessages).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({
        data: [],
        total: 0,
        page: 1,
        pageSize: 50,
        hasMore: false,
      }), 100))
    );

    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Should show loading indicator
    expect(screen.getByText('Loading messages...')).toBeInTheDocument();
  });

  it('handles message sending', async () => {
    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/Type a message/);
    const sendButton = screen.getByText('➤');

    // Type a message
    fireEvent.change(messageInput, { target: { value: 'Test message' } });
    
    // Send the message
    fireEvent.click(sendButton);

    // Check if the message service was called
    const mockMessageService = await import('../services/messageService');
    expect(mockMessageService.messageService.sendMessage).toHaveBeenCalledWith(
      'Test message',
      undefined
    );
  });

  it('shows error state when message fails to send', async () => {
    // Mock error response
    const mockMessageService = await import('../services/messageService');
    vi.mocked(mockMessageService.messageService.sendMessage).mockRejectedValue(
      new Error('Failed to send message')
    );

    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    const messageInput = screen.getByPlaceholderText(/Type a message/);
    const sendButton = screen.getByText('➤');

    // Type and send a message
    fireEvent.change(messageInput, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);

    // Should show error (in a real implementation)
    // This would depend on how error handling is implemented
  });

  it('displays connection status', async () => {
    // Mock disconnected state
    const mockWebSocketService = await import('../services/websocketService');
    Object.defineProperty(mockWebSocketService.websocketService, 'isConnected', {
      value: false,
      writable: true,
    });

    render(
      <TestWrapper>
        <DiscussModule />
      </TestWrapper>
    );

    // Should show reconnecting message
    expect(screen.getByText('Reconnecting...')).toBeInTheDocument();
  });
});
