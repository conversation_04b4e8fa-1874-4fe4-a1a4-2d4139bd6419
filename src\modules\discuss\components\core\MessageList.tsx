import React, { useEffect, useRef, useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { Message } from './Message';
import type { Message as MessageType, User } from '../../types';

export interface MessageListProps {
  messages: MessageType[];
  users: User[];
  currentUserId: string;
  channelId?: string;
  isLoading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  onReaction?: (messageId: string, emoji: string) => void;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onPin?: (messageId: string) => void;
  className?: string;
  'data-testid'?: string;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  users,
  currentUserId,
  channelId,
  isLoading = false,
  hasMore = false,
  onLoadMore,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onPin,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  // Check if user is near bottom to determine auto-scroll behavior
  const handleScroll = () => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setAutoScroll(isNearBottom);

    // Load more messages when scrolling to top
    if (scrollTop === 0 && hasMore && onLoadMore && !isLoading) {
      onLoadMore();
    }
  };

  const getUserById = (userId: string): User | undefined => {
    return users.find(user => user.id === userId);
  };

  const groupMessagesByDate = (messages: MessageType[]) => {
    const groups: { [date: string]: MessageType[] } = {};
    
    messages.forEach(message => {
      const date = new Date(message.timestamp).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return groups;
  };

  const shouldShowAvatar = (message: MessageType, previousMessage?: MessageType): boolean => {
    if (!previousMessage) return true;
    if (previousMessage.authorId !== message.authorId) return true;
    
    const timeDiff = new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime();
    return timeDiff > 5 * 60 * 1000; // 5 minutes
  };

  const formatDateHeader = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString([], { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  const messageGroups = groupMessagesByDate(messages);
  const sortedDates = Object.keys(messageGroups).sort((a, b) => 
    new Date(a).getTime() - new Date(b).getTime()
  );

  if (messages.length === 0 && !isLoading) {
    return (
      <div
        className={`flex-1 flex items-center justify-center ${className}`}
        data-testid={testId}
      >
        <div className="text-center py-12">
          <div className="text-4xl mb-4">💬</div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            No messages yet
          </h3>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Be the first to start the conversation!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex-1 overflow-y-auto max-h-full ${className}`}
      ref={messagesContainerRef}
      onScroll={handleScroll}
      data-testid={testId}
    >
      {/* Loading indicator for loading more messages */}
      {isLoading && hasMore && (
        <div className="text-center py-4">
          <div className="inline-flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2" style={{ borderColor: colors.primary }}></div>
            <span className="text-sm" style={{ color: colors.textSecondary }}>
              Loading more messages...
            </span>
          </div>
        </div>
      )}

      {/* Messages grouped by date */}
      <div className="px-6 py-4">
        {sortedDates.map(dateString => (
          <div key={dateString}>
            {/* Date separator */}
            <div className="flex items-center justify-center my-6">
              <div
                className="px-4 py-2 rounded-full text-xs font-medium"
                style={{
                  backgroundColor: colors.surfaceSecondary,
                  color: colors.textSecondary,
                }}
              >
                {formatDateHeader(dateString)}
              </div>
            </div>

            {/* Messages for this date */}
            {messageGroups[dateString].map((message, index) => {
              const author = getUserById(message.authorId);
              const previousMessage = index > 0 ? messageGroups[dateString][index - 1] : undefined;
              const showAvatar = shouldShowAvatar(message, previousMessage);

              if (!author) {
                console.warn(`Author not found for message ${message.id}`);
                return null;
              }

              return (
                <Message
                  key={message.id}
                  message={message}
                  author={author}
                  showAvatar={showAvatar}
                  showTimestamp={showAvatar}
                  isCompact={!showAvatar}
                  onReaction={onReaction}
                  onReply={onReply}
                  onEdit={message.authorId === currentUserId ? onEdit : undefined}
                  onDelete={message.authorId === currentUserId ? onDelete : undefined}
                  onPin={onPin}
                />
              );
            })}
          </div>
        ))}
      </div>

      {/* Scroll to bottom anchor */}
      <div ref={messagesEndRef} />

      {/* Loading indicator for initial load */}
      {isLoading && messages.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{ borderColor: colors.primary }}></div>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              Loading messages...
            </p>
          </div>
        </div>
      )}

      {/* Scroll to bottom button */}
      {!autoScroll && (
        <div className="fixed bottom-24 right-8">
          <button
            onClick={() => {
              setAutoScroll(true);
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="p-3 rounded-full shadow-lg hover:shadow-xl transition-shadow"
            style={{
              backgroundColor: colors.primary,
              color: 'white',
            }}
            title="Scroll to bottom"
          >
            ⬇️
          </button>
        </div>
      )}
    </div>
  );
};
