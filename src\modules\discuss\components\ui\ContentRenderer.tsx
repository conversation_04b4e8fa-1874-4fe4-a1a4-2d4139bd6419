import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface ContentRendererProps {
  content: string;
  attachments?: Array<{
    id: string;
    name: string;
    type: 'image' | 'video' | 'audio' | 'document' | 'other';
    url: string;
    size: number;
    mimeType: string;
    thumbnail?: string;
  }>;
  mentions?: Array<{
    id: string;
    userId: string;
    username: string;
    start: number;
    end: number;
  }>;
  links?: Array<{
    url: string;
    title?: string;
    description?: string;
    image?: string;
    start: number;
    end: number;
  }>;
  enablePreview?: boolean;
  maxImageWidth?: number;
  maxVideoWidth?: number;
  className?: string;
  'data-testid'?: string;
}

export const ContentRenderer: React.FC<ContentRendererProps> = ({
  content,
  attachments = [],
  mentions = [],
  links = [],
  enablePreview = true,
  maxImageWidth = 400,
  maxVideoWidth = 500,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [expandedImages, setExpandedImages] = useState<Set<string>>(new Set());
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string, mimeType: string) => {
    if (type === 'image') return '🖼️';
    if (type === 'video') return '🎥';
    if (type === 'audio') return '🎵';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📽️';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '🗜️';
    return '📎';
  };

  const renderFormattedText = (text: string) => {
    let formattedText = text;

    // Replace mentions
    mentions.forEach(mention => {
      const mentionText = `@${mention.username}`;
      formattedText = formattedText.replace(
        new RegExp(`@${mention.username}`, 'g'),
        `<span class="mention" style="color: ${colors.primary}; background-color: ${colors.primary}20; padding: 2px 4px; border-radius: 4px; font-weight: 500;">@${mention.username}</span>`
      );
    });

    // Format basic markdown-style text
    formattedText = formattedText
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Inline code
      .replace(/`(.*?)`/g, `<code style="background-color: ${colors.backgroundSecondary}; padding: 2px 4px; border-radius: 4px; font-family: monospace; font-size: 0.9em;">$1</code>`)
      // Links (basic detection)
      .replace(
        /(https?:\/\/[^\s]+)/g,
        `<a href="$1" target="_blank" rel="noopener noreferrer" style="color: ${colors.primary}; text-decoration: underline;">$1</a>`
      );

    return { __html: formattedText };
  };

  const toggleImageExpansion = (imageId: string) => {
    setExpandedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  };

  const handleImageLoad = (imageId: string) => {
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
  };

  const handleImageLoadStart = (imageId: string) => {
    setLoadingImages(prev => new Set(prev).add(imageId));
  };

  const renderAttachment = (attachment: any) => {
    const isExpanded = expandedImages.has(attachment.id);
    const isLoading = loadingImages.has(attachment.id);

    switch (attachment.type) {
      case 'image':
        return (
          <div key={attachment.id} className="mt-2">
            <div
              className="relative inline-block cursor-pointer rounded-lg overflow-hidden border"
              style={{ borderColor: colors.border }}
              onClick={() => toggleImageExpansion(attachment.id)}
            >
              {isLoading && (
                <div
                  className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800"
                  style={{ minHeight: '100px' }}
                >
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: colors.primary }}></div>
                </div>
              )}
              <img
                src={attachment.url}
                alt={attachment.name}
                className={`transition-all duration-200 ${isExpanded ? 'max-w-none' : `max-w-[${maxImageWidth}px]`} max-h-96 object-contain`}
                style={{ 
                  maxWidth: isExpanded ? 'none' : `${maxImageWidth}px`,
                  display: isLoading ? 'none' : 'block'
                }}
                onLoad={() => handleImageLoad(attachment.id)}
                onLoadStart={() => handleImageLoadStart(attachment.id)}
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {isExpanded ? 'Click to shrink' : 'Click to expand'}
              </div>
            </div>
            <p className="text-xs mt-1" style={{ color: colors.textSecondary }}>
              {attachment.name} • {formatFileSize(attachment.size)}
            </p>
          </div>
        );

      case 'video':
        return (
          <div key={attachment.id} className="mt-2">
            <video
              controls
              className="rounded-lg border"
              style={{ 
                maxWidth: `${maxVideoWidth}px`,
                borderColor: colors.border 
              }}
            >
              <source src={attachment.url} type={attachment.mimeType} />
              Your browser does not support the video tag.
            </video>
            <p className="text-xs mt-1" style={{ color: colors.textSecondary }}>
              {attachment.name} • {formatFileSize(attachment.size)}
            </p>
          </div>
        );

      case 'audio':
        return (
          <div key={attachment.id} className="mt-2">
            <div
              className="flex items-center space-x-3 p-3 border rounded-lg"
              style={{
                borderColor: colors.border,
                backgroundColor: colors.backgroundSecondary,
              }}
            >
              <div className="text-2xl">{getFileIcon(attachment.type, attachment.mimeType)}</div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                  {attachment.name}
                </p>
                <audio controls className="w-full mt-2">
                  <source src={attachment.url} type={attachment.mimeType} />
                  Your browser does not support the audio tag.
                </audio>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div key={attachment.id} className="mt-2">
            <div
              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
              style={{
                borderColor: colors.border,
                backgroundColor: colors.backgroundSecondary,
              }}
              onClick={() => window.open(attachment.url, '_blank')}
            >
              <div className="text-2xl">{getFileIcon(attachment.type, attachment.mimeType)}</div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                  {attachment.name}
                </p>
                <p className="text-xs" style={{ color: colors.textSecondary }}>
                  {formatFileSize(attachment.size)} • Click to download
                </p>
              </div>
              <div className="text-sm" style={{ color: colors.primary }}>
                ⬇️
              </div>
            </div>
          </div>
        );
    }
  };

  const renderLinkPreviews = () => {
    if (!enablePreview || links.length === 0) return null;

    return (
      <div className="mt-2 space-y-2">
        {links.map((link, index) => (
          <div
            key={index}
            className="border rounded-lg overflow-hidden hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
            style={{ borderColor: colors.border }}
            onClick={() => window.open(link.url, '_blank')}
          >
            {link.image && (
              <img
                src={link.image}
                alt={link.title || link.url}
                className="w-full h-32 object-cover"
              />
            )}
            <div className="p-3">
              {link.title && (
                <h4 className="font-medium text-sm mb-1" style={{ color: colors.text }}>
                  {link.title}
                </h4>
              )}
              {link.description && (
                <p className="text-xs mb-2" style={{ color: colors.textSecondary }}>
                  {link.description}
                </p>
              )}
              <p className="text-xs" style={{ color: colors.primary }}>
                {link.url}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={className} data-testid={testId}>
      {/* Main Content */}
      <div
        className="text-sm leading-relaxed"
        style={{ color: colors.text }}
        dangerouslySetInnerHTML={renderFormattedText(content)}
      />

      {/* Attachments */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          {attachments.map(renderAttachment)}
        </div>
      )}

      {/* Link Previews */}
      {renderLinkPreviews()}
    </div>
  );
};
