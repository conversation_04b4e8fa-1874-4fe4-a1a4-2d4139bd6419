import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import DynamicAppHeader from '../DynamicAppHeader/DynamicAppHeader';
import DynamicAppBottomBar from '../DynamicAppBottomBar/DynamicAppBottomBar';
import { GridIcon, ListIcon, BellIcon } from '../../icons';

// Mock the theme store
const mockColors = {
  surface: '#ffffff',
  border: '#e5e7eb',
  text: '#111827',
  textSecondary: '#6b7280',
  mutedForeground: '#9ca3af',
  primary: '#3b82f6',
  primaryForeground: '#ffffff',
  hover: '#f3f4f6',
  background: '#f9fafb',
  error: '#ef4444',
  errorForeground: '#ffffff',
};

// Mock the useThemeStore hook
const mockUseThemeStore = () => ({ colors: mockColors });

// Mock the useNavigate hook
const mockNavigate = () => {};

// Mock modules
jest.mock('../../../stores/themeStore', () => ({
  useThemeStore: mockUseThemeStore,
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const mockApp = {
  name: 'Test App',
  icon: <GridIcon className="w-6 h-6" />,
  navLinks: [
    { label: 'Dashboard', href: '/dashboard', isActive: true },
    { label: 'Projects', href: '/projects' },
  ],
};

const mockUser = {
  name: 'Test User',
  avatar: <div className="w-8 h-8 bg-blue-500 rounded-full">TU</div>,
  notifications: [
    { count: 3, icon: <BellIcon className="w-5 h-5" /> },
  ],
};

const mockView = {
  title: 'Test Dashboard',
  actions: [
    { label: 'New', onClick: () => {}, isPrimary: true },
    { label: 'Export', onClick: () => {} },
  ],
  search: {
    onSearch: () => {},
  },
  pagination: {
    currentRange: '1-20 of 100',
    onNext: () => {},
    onPrev: () => {},
  },
  viewModes: [
    { name: 'grid', icon: <GridIcon className="w-4 h-4" /> },
    { name: 'list', icon: <ListIcon className="w-4 h-4" /> },
  ],
  activeViewMode: 'grid',
};

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('Component Separation Tests', () => {
  describe('DynamicAppHeader Integration', () => {
    it('renders header with integrated bottom bar', () => {
      renderWithRouter(
        <DynamicAppHeader 
          app={mockApp}
          user={mockUser}
          view={mockView}
        />
      );
      
      // Check header elements
      expect(screen.getByText('Test App')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Projects')).toBeInTheDocument();
      
      // Check bottom bar elements (integrated)
      expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.getByText('Export')).toBeInTheDocument();
      expect(screen.getByText('1-20 of 100')).toBeInTheDocument();
    });
  });

  describe('DynamicAppBottomBar Standalone', () => {
    it('renders bottom bar independently', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      // Check bottom bar elements
      expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.getByText('Export')).toBeInTheDocument();
      expect(screen.getByText('1-20 of 100')).toBeInTheDocument();
      
      // Check view mode switcher
      expect(screen.getByLabelText('Switch to grid view')).toBeInTheDocument();
      expect(screen.getByLabelText('Switch to list view')).toBeInTheDocument();
    });

    it('can be used without header', () => {
      renderWithRouter(
        <div>
          <div className="p-4 bg-gray-100">
            <h1>Custom Header</h1>
          </div>
          <DynamicAppBottomBar view={mockView} />
        </div>
      );
      
      expect(screen.getByText('Custom Header')).toBeInTheDocument();
      expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
    });
  });

  describe('Component Separation Benefits', () => {
    it('allows different configurations for same bottom bar', () => {
      const view1 = { ...mockView, title: 'View 1' };
      const view2 = { ...mockView, title: 'View 2' };
      
      const { rerender } = renderWithRouter(<DynamicAppBottomBar view={view1} />);
      expect(screen.getByText('View 1')).toBeInTheDocument();
      
      rerender(<BrowserRouter><DynamicAppBottomBar view={view2} /></BrowserRouter>);
      expect(screen.getByText('View 2')).toBeInTheDocument();
    });

    it('supports custom styling on bottom bar', () => {
      const { container } = renderWithRouter(
        <DynamicAppBottomBar 
          view={mockView} 
          className="custom-bottom-bar"
        />
      );
      
      expect(container.querySelector('.custom-bottom-bar')).toBeInTheDocument();
    });

    it('maintains consistent interface between integrated and standalone', () => {
      // Test that both ways of using the bottom bar have the same interface
      const { container: integratedContainer } = renderWithRouter(
        <DynamicAppHeader 
          app={mockApp}
          user={mockUser}
          view={mockView}
        />
      );
      
      const { container: standaloneContainer } = renderWithRouter(
        <DynamicAppBottomBar view={mockView} />
      );
      
      // Both should have the same bottom bar elements
      const integratedBottomBar = integratedContainer.querySelector('[data-testid="bottom-bar"]') ||
                                  integratedContainer.querySelector('.border-t');
      const standaloneBottomBar = standaloneContainer.querySelector('.border-t');
      
      expect(integratedBottomBar).toBeTruthy();
      expect(standaloneBottomBar).toBeTruthy();
    });
  });

  describe('Responsive Behavior', () => {
    it('renders mobile search toggle in bottom bar', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      expect(screen.getByLabelText('Toggle search')).toBeInTheDocument();
    });

    it('renders floating action button for primary actions', () => {
      renderWithRouter(<DynamicAppBottomBar view={mockView} />);
      
      // Should have floating action button for primary action
      const floatingButtons = screen.getAllByLabelText('New');
      expect(floatingButtons.length).toBeGreaterThan(0);
    });
  });
});