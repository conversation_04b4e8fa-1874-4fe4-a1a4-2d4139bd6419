// WebSocket service for real-time communication
import type { 
  MessageEvent, 
  PresenceEvent, 
  ChannelEvent,
  PresenceInfo 
} from '../types';

export type WebSocketEventType = 
  | 'message_created'
  | 'message_updated' 
  | 'message_deleted'
  | 'user_online'
  | 'user_offline'
  | 'user_typing'
  | 'user_stopped_typing'
  | 'channel_created'
  | 'channel_updated'
  | 'channel_deleted'
  | 'user_joined'
  | 'user_left';

export type WebSocketEventHandler = (event: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private eventHandlers: Map<WebSocketEventType, WebSocketEventHandler[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private currentUserId: string | null = null;

  // Connect to WebSocket server
  connect(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;
      this.currentUserId = userId;

      // In development, we'll simulate WebSocket with a mock connection
      if (import.meta.env.DEV) {
        this.simulateConnection();
        resolve();
        return;
      }

      try {
        const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/discuss/ws`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // Send authentication
          this.send('authenticate', { userId });
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket disconnected');
          this.isConnecting = false;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Simulate WebSocket connection for development
  private simulateConnection(): void {
    console.log('Simulating WebSocket connection for development');
    this.isConnecting = false;
    
    // Simulate some real-time events for testing
    setTimeout(() => {
      this.emit('user_online', {
        type: 'user_online',
        userId: '3',
        presence: {
          userId: '3',
          status: 'online',
          lastSeen: new Date(),
          isTyping: false,
        },
      });
    }, 2000);

    setTimeout(() => {
      this.emit('user_typing', {
        type: 'user_typing',
        userId: '2',
        channelId: 'general',
        presence: {
          userId: '2',
          status: 'online',
          lastSeen: new Date(),
          isTyping: true,
          currentChannel: 'general',
        },
      });
    }, 5000);

    setTimeout(() => {
      this.emit('user_stopped_typing', {
        type: 'user_stopped_typing',
        userId: '2',
        channelId: 'general',
        presence: {
          userId: '2',
          status: 'online',
          lastSeen: new Date(),
          isTyping: false,
        },
      });
    }, 8000);
  }

  // Disconnect from WebSocket
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.currentUserId = null;
    this.eventHandlers.clear();
  }

  // Send message through WebSocket
  private send(type: string, data: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, data }));
    } else if (import.meta.env.DEV) {
      // In development, just log the message
      console.log('WebSocket send (simulated):', { type, data });
    }
  }

  // Handle incoming WebSocket messages
  private handleMessage(message: any): void {
    const { type, data } = message;
    this.emit(type, data);
  }

  // Emit event to registered handlers
  private emit(type: WebSocketEventType, data: any): void {
    const handlers = this.eventHandlers.get(type) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in WebSocket event handler for ${type}:`, error);
      }
    });
  }

  // Register event handler
  on(type: WebSocketEventType, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(type)) {
      this.eventHandlers.set(type, []);
    }
    this.eventHandlers.get(type)!.push(handler);
  }

  // Unregister event handler
  off(type: WebSocketEventType, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Send typing indicator
  sendTyping(channelId: string, isTyping: boolean): void {
    this.send('typing', {
      channelId,
      isTyping,
      userId: this.currentUserId,
    });
  }

  // Update user presence
  updatePresence(status: 'online' | 'offline' | 'away' | 'busy'): void {
    this.send('presence', {
      userId: this.currentUserId,
      status,
      timestamp: new Date(),
    });
  }

  // Join channel for real-time updates
  joinChannel(channelId: string): void {
    this.send('join_channel', {
      channelId,
      userId: this.currentUserId,
    });
  }

  // Leave channel
  leaveChannel(channelId: string): void {
    this.send('leave_channel', {
      channelId,
      userId: this.currentUserId,
    });
  }

  // Attempt to reconnect
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      if (this.currentUserId) {
        this.connect(this.currentUserId).catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  // Get connection status
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN || import.meta.env.DEV;
  }

  // Get current user ID
  get userId(): string | null {
    return this.currentUserId;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
