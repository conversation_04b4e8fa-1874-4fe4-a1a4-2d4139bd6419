import React from 'react';
import { useThemeStore } from '../stores/themeStore';
import type { ThemeColors } from '../stores/themeStore';

/**
 * Hook for accessing the complete theme state and actions
 * This replaces the old useTheme hook that relied on React Context
 */
export function useTheme() {
  const {
    theme,
    variant,
    colors,
    isDark,
    isSystemTheme,
    setTheme,
    setVariant,
    toggleTheme,
    getCSSVariables,
    applyCSSVariables,
    getThemeClass,
    getColor,
    getContrastColor,
  } = useThemeStore();

  return {
    theme,
    variant,
    colors,
    isDark,
    isSystemTheme,
    setTheme,
    setVariant,
    toggleTheme,
    getCSSVariables,
    applyCSSVariables,
    getThemeClass,
    getColor,
    getContrastColor,
  };
}

/**
 * Hook for accessing only theme colors
 * This replaces the old useThemeColors hook
 */
export function useThemeColors() {
  const colors = useThemeStore(state => state.colors);
  return colors;
}

/**
 * Hook for theme-aware styling utilities
 * This replaces the old useThemeStyles hook
 */
export function useThemeStyles() {
  const { colors, isDark, variant, getColor, getContrastColor } =
    useThemeStore();

  return {
    colors,
    isDark,
    variant,
    getColor,
    getContrastColor,
    applyThemeStyles: (element: HTMLElement) => {
      const variables = useThemeStore.getState().getCSSVariables();
      Object.entries(variables).forEach(([property, value]) => {
        element.style.setProperty(property, value);
      });
    },
  };
}

/**
 * Utility function to create theme-aware CSS-in-JS styles
 * This replaces the old createThemeStyles function
 */
export function createThemeStyles(colors: ThemeColors) {
  return {
    primary: {
      backgroundColor: colors.primary,
      color: colors.primaryForeground,
    },
    secondary: {
      backgroundColor: colors.secondary,
      color: colors.secondaryForeground,
    },
    surface: {
      backgroundColor: colors.surface,
      color: colors.text,
    },
    card: {
      backgroundColor: colors.card,
      color: colors.cardForeground,
      border: `1px solid ${colors.border}`,
    },
    input: {
      backgroundColor: colors.input,
      color: colors.inputForeground,
      border: `1px solid ${colors.border}`,
    },
    button: {
      primary: {
        backgroundColor: colors.primary,
        color: colors.primaryForeground,
        border: `1px solid ${colors.primary}`,
      },
      secondary: {
        backgroundColor: colors.secondary,
        color: colors.secondaryForeground,
        border: `1px solid ${colors.secondary}`,
      },
      outline: {
        backgroundColor: 'transparent',
        color: colors.primary,
        border: `1px solid ${colors.border}`,
      },
      ghost: {
        backgroundColor: 'transparent',
        color: colors.text,
        border: 'none',
      },
    },
    text: {
      primary: { color: colors.text },
      secondary: { color: colors.textSecondary },
      muted: { color: colors.textMuted },
      inverse: { color: colors.textInverse },
    },
    border: {
      default: { borderColor: colors.border },
      secondary: { borderColor: colors.borderSecondary },
      focus: { borderColor: colors.borderFocus },
    },
    state: {
      hover: { backgroundColor: colors.hover },
      active: { backgroundColor: colors.active },
      focus: { backgroundColor: colors.focus },
      disabled: {
        backgroundColor: colors.disabled,
        color: colors.disabledForeground,
      },
    },
    semantic: {
      success: {
        backgroundColor: colors.success,
        color: colors.successForeground,
      },
      warning: {
        backgroundColor: colors.warning,
        color: colors.warningForeground,
      },
      error: {
        backgroundColor: colors.error,
        color: colors.errorForeground,
      },
      info: {
        backgroundColor: colors.info,
        color: colors.infoForeground,
      },
    },
  };
}

/**
 * Hook for getting theme-aware CSS-in-JS styles
 */
export function useThemeStylesCSS() {
  const colors = useThemeColors();
  return createThemeStyles(colors);
}

/**
 * Hook for theme initialization
 * This should be called once at the app root level
 */
export function useThemeInitialization(options?: {
  defaultTheme?: 'light' | 'dark' | 'system';
  defaultVariant?: 'default' | 'high-contrast' | 'colorblind-friendly';
  enableSystemDetection?: boolean;
  enableCSSVariables?: boolean;
}) {
  const { initialize, isInitialized } = useThemeStore();

  // Initialize theme on mount if not already initialized
  React.useEffect(() => {
    if (!isInitialized) {
      initialize(options);
    }
  }, [initialize, isInitialized, options]);

  return { isInitialized };
}

/**
 * Hook for theme cleanup
 * This should be called when the app unmounts
 */
export function useThemeCleanup() {
  const cleanup = useThemeStore(state => state.cleanup);

  React.useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);
}
