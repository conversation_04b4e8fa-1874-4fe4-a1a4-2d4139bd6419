/**
 * useErrorBoundary Hook
 * React hook for programmatically triggering error boundaries and handling errors in functional components
 */

import React, { useCallback, useRef, useState } from 'react';
import type { AppError } from '../../../utils/errorTypes';
import { createAppError } from '../../../utils/errorTypes';

export interface UseErrorBoundaryOptions {
  onError?: (error: AppError) => void;
  componentName?: string;
  enableAutoReport?: boolean;
}

export interface UseErrorBoundaryReturn {
  captureError: (error: Error | string | unknown, context?: Record<string, any>) => void;
  resetError: () => void;
  showBoundary: (error: Error | string | unknown) => void;
}

/**
 * Hook for programmatic error boundary interaction
 */
export function useErrorBoundary(options: UseErrorBoundaryOptions = {}): UseErrorBoundaryReturn {
  const { onError, componentName, enableAutoReport = true } = options;
  const errorRef = useRef<Error | null>(null);

  const captureError = useCallback((
    error: Error | string | unknown,
    context?: Record<string, any>
  ) => {
    const appError = createAppError(error, undefined, {
      component: componentName,
      route: window.location.pathname,
      ...context,
    });

    // Store error reference
    errorRef.current = appError;

    // Call custom error handler
    if (onError) {
      onError(appError);
    }

    // Auto-report if enabled
    if (enableAutoReport) {
      import('../../../utils/errorReporting').then(({ reportError }) => {
        reportError(appError, {
          captureMethod: 'useErrorBoundary',
          componentName,
          ...context,
        });
      });
    }

    // Log to console for development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error captured by useErrorBoundary:', appError);
    }
  }, [onError, componentName, enableAutoReport]);

  const resetError = useCallback(() => {
    errorRef.current = null;
  }, []);

  const showBoundary = useCallback((error: Error | string | unknown) => {
    // Convert to Error object if needed
    const errorObj = error instanceof Error 
      ? error 
      : new Error(typeof error === 'string' ? error : 'Unknown error');
    
    // Throw the error to trigger the nearest error boundary
    throw errorObj;
  }, []);

  return {
    captureError,
    resetError,
    showBoundary,
  };
}

/**
 * Hook for error boundary state management
 */
export function useErrorBoundaryState() {
  const [error, setError] = useState<AppError | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const captureError = useCallback((error: Error | string | unknown) => {
    const appError = createAppError(error);
    setError(appError);
  }, []);

  const resetError = useCallback(() => {
    setError(null);
    setIsRecovering(false);
  }, []);

  const retry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    setIsRecovering(true);
    
    // Reset after a short delay to allow for re-rendering
    setTimeout(() => {
      resetError();
    }, 100);
  }, [resetError]);

  return {
    error,
    isRecovering,
    retryCount,
    captureError,
    resetError,
    retry,
    hasError: error !== null,
  };
}

/**
 * Hook for async error handling
 */
export function useAsyncError() {
  const { showBoundary } = useErrorBoundary();

  return useCallback((error: Error | string | unknown) => {
    // Schedule error to be thrown in next tick to ensure it's caught by error boundary
    setTimeout(() => {
      showBoundary(error);
    }, 0);
  }, [showBoundary]);
}

/**
 * Higher-order hook for wrapping async operations with error handling
 */
export function useErrorHandler<T extends (...args: any[]) => Promise<any>>(
  asyncFn: T,
  options: UseErrorBoundaryOptions = {}
): T {
  const { captureError } = useErrorBoundary(options);

  return useCallback(async (...args: Parameters<T>) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      captureError(error, {
        functionName: asyncFn.name,
        arguments: args,
        timestamp: new Date(),
      });
      throw error; // Re-throw to maintain original behavior
    }
  }, [asyncFn, captureError]) as T;
}