/// <reference types="vitest/config" />
/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [tailwindcss(), react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    include: ['**/*.test.{ts,tsx}'],
    exclude: [
      '**/stories/**',
      '**/*.stories.*',
      '**/storybook/**',
      '**/node_modules/**',
    ],
    server: {
      deps: {
        inline: ['msw'],
      },
    },
  },
});
